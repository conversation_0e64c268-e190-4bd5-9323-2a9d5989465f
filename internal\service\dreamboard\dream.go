package dreamboard

import (
	"context"
	"log"
	"sort"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Dream CRUD

// CreateDream creates a new dream in a dreamboard
func (s *service) CreateDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) (*CreateDreamResponseDTO, error) {
	if err := dream.Validate(); err != nil {
		return nil, err
	}

	// Validate that the category exists in the dreamboard
	categoryExists := false
	for _, category := range board.Categories {
		if category.Identifier == dream.Category.String() {
			categoryExists = true
			break
		}
	}
	if !categoryExists {
		return nil, errors.New(errors.Service, "category does not exist in dreamboard", errors.Validation, nil)
	}

	dream.CreatedAt = time.Now()
	dream.UpdatedAt = dream.CreatedAt
	dream.Color = s.getColor(dream.Category.String())

	// Set default values for shared dream fields
	if dream.IsShared {
		dream.CreatorUserID = board.User
		dream.FundingStatus = dreamboard.FundingStatusSharedOpenForParticipants
		dream.CurrentRaisedAmount = 0
	} else {
		dream.FundingStatus = dreamboard.FundingStatusPersonalActive
		dream.CurrentRaisedAmount = 0
	}

	// Calculate initial duration
	if dream.MonthlySavings > 0 {
		duration := int(dream.EstimatedCost / dream.MonthlySavings)
		dream.CalculatedDurationMonths = &duration
	}

	if err := s.Repository.CreateDream(ctx, board.ObjectID, dream); err != nil {
		return nil, err
	}

	// Get updated dreamboard with the new dreams
	updatedBoard, err := s.Repository.Find(ctx, board.ObjectID)
	if err != nil {
		return nil, err
	}

	// Convert ObjectID to hex ID for response
	updatedBoard.ID = updatedBoard.ObjectID.Hex()

	for _, dream := range updatedBoard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Recompute totals and update the board since we added a dream
	updatedBoard.ComputeTotals()
	if err := s.Repository.Update(ctx, updatedBoard); err != nil {
		return nil, err
	}

	// Find the newly created dream by ID
	var createdDream *dreamboard.Dream
	for _, d := range updatedBoard.Dreams {
		if d.ID == dream.ID {
			createdDream = d
			break
		}
	}

	// Prepare response
	response := &CreateDreamResponseDTO{
		ID:                       createdDream.ID,
		Category:                 createdDream.Category,
		Color:                    createdDream.Color,
		Title:                    createdDream.Title,
		TimeFrame:                createdDream.TimeFrame,
		Deadline:                 createdDream.Deadline,
		EstimatedCost:            createdDream.EstimatedCost,
		MonthlySavings:           createdDream.MonthlySavings,
		MoneySource:              createdDream.MoneySource,
		Completed:                createdDream.Completed,
		CustomMoneySource:        createdDream.CustomMoneySource,
		CreatedAt:                createdDream.CreatedAt,
		UpdatedAt:                createdDream.UpdatedAt,
		IsShared:                 createdDream.IsShared,
		CreatorUserID:            createdDream.CreatorUserID,
		CurrentRaisedAmount:      createdDream.CurrentRaisedAmount,
		FundingStatus:            createdDream.FundingStatus,
		CalculatedDurationMonths: createdDream.CalculatedDurationMonths,
		// ShareCode will be populated below if applicable
	}

	// If it's a shared dream, create ShareLink and creator's Contribution
	if dream.IsShared {
		if dream.ID != "" {
			// Create ShareLink
			sharedLink, err := s.CreateShareLink(ctx, dream.ID)
			if err != nil {
				return nil, err
			}

			// Return the token for the client to use in the invite URL
			response.Code = &sharedLink.Code

			// Create creator's contribution
			isCreator := true // Creator is also a contributor
			_, err = s.JoinSharedDream(ctx, sharedLink.Code, board.User, isCreator, dream.MonthlySavings)
			if err != nil {
				return nil, err
			}
		}
	}

	// Check for Dream achievement
	if err := s.GamificationService.CheckDreamAchievement(ctx, board.User); err != nil {
		// Log error but don't fail the entire operation
		log.Printf("Failed to check dream achievement for user %s: %v", board.User, err)
	}

	return response, nil
}

// FindDream retrieves a specific dream from a dreamboard
func (s *service) FindDream(ctx context.Context, board *dreamboard.Dreamboard, dreamID string) (*dreamboard.Dream, error) {
	objID, err := primitive.ObjectIDFromHex(dreamID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dream ID", errors.BadRequest, err)
	}

	dream, err := s.Repository.FindDream(ctx, board.ObjectID, objID)
	if err != nil {
		return nil, err
	}

	if dream != nil && !dream.ObjectID.IsZero() {
		dream.ID = dream.ObjectID.Hex()
	}

	return dream, nil
}

// FindPersonalDreams retrieves all personal (non-shared) dreams for a user
func (s *service) FindPersonalDreams(ctx context.Context, userID string) ([]*dreamboard.Dream, error) {
	board, err := s.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	var personalDreams []*dreamboard.Dream
	for _, dream := range board.Dreams {
		if !dream.IsShared {
			personalDreams = append(personalDreams, dream)
		}
	}

	return personalDreams, nil
}

// FindSharedDreams retrieves all shared dreams where the user is creator or active contributor
func (s *service) FindSharedDreams(ctx context.Context, userID string) ([]*dreamboard.Dream, error) {
	// Get user's contributions
	contributions, err := s.FindContributionsByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	var sharedDreams []*dreamboard.Dream
	dreamIDs := make(map[string]bool) // To avoid duplicates

	// Get dreams where user is an active contributor
	for _, contribution := range contributions {
		if contribution.Status == dreamboard.ContributionStatusActive {
			if !dreamIDs[contribution.DreamID] {
				// Find the dream in the user's dreamboard or other users' dreamboards
				dream, err := s.findDreamByID(ctx, contribution.DreamID)
				if err == nil && dream != nil {
					sharedDreams = append(sharedDreams, dream)
					dreamIDs[contribution.DreamID] = true
				}
			}
		}
	}

	return sharedDreams, nil
}

// FindDreamDetails retrieves comprehensive dashboard information for a shared or personal dream
func (s *service) FindDreamDetails(ctx context.Context, dreamID string, userID string) (*DreamDetails, error) {
	// Get foundedDream details first
	foundedDream, err := s.findDreamByID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	// Initialize dreamDetails DTO
	dreamDetails := &DreamDetails{}

	// Determine if the requesting user is the creator of this specific dream
	// For personal dreams, the creator is the user who owns the dreamboard.
	// For shared dreams, the CreatorUserID field in the dream model is used.
	isViewingUserCreator := false
	if foundedDream.IsShared {
		isViewingUserCreator = foundedDream.CreatorUserID == userID
	} else {
		// For personal dreams, find the board and check ownership
		board, err := s.findDreamboardByDreamID(ctx, dreamID)
		if err != nil {
			// If board not found, it's an issue, but proceed with dream data if available
			log.Printf("Error finding dreamboard for personal dream %s: %v", dreamID, err)
		} else {
			isViewingUserCreator = board.User == userID
		}
	}

	// Populate DreamDetailsDTO
	dream := DreamDTO{
		ID:                      foundedDream.ID,
		Title:                   foundedDream.Title,
		IsCreator:               isViewingUserCreator, // This is about the *viewing* user
		TotalCost:               foundedDream.EstimatedCost,
		RaisedAmount:            foundedDream.CurrentRaisedAmount, // This will be enhanced later with financial sheet data for shared dreams
		RemainingAmount:         foundedDream.EstimatedCost - foundedDream.CurrentRaisedAmount,
		EstimatedDurationMonths: foundedDream.CalculatedDurationMonths,
		FundingStatus:           string(foundedDream.FundingStatus),
	}

	// Handle shared dream specific details
	if foundedDream.IsShared {
		// Get active contributions
		activeContributions, err := s.FindActiveContributionsByDreamID(ctx, dreamID)
		if err != nil {
			return nil, err
		}

		// Get share link details
		shareLink, err := s.FindShareLink(ctx, dreamID)
		if err != nil {
			// If a shared dream doesn't have a share link, it's an inconsistent state
			// but we can still return the dashboard with available info.
			// Log the error and continue.
			log.Printf("Error fetching share link for dream %s: %v. Proceeding without share link details.", dreamID, err)
			dream.ShareLinkDetails = ShareLinkDTO{} // Empty share link
		} else {
			dream.ShareLinkDetails = ShareLinkDTO{
				Code:      shareLink.Code,
				IsEnabled: shareLink.IsEnabled,
			}
		}

		// Build contributors list
		contributors := make([]ContributorDTO, 0, len(activeContributions))
		for _, contribution := range activeContributions {
			// User details (UserName, UserAvatarURL) will be filled by the controller
			currentMonthPaidAmount, err := s.getCurrentMonthPaidAmount(ctx, contribution.ContributorUserID, dreamID)
			if err != nil {
				return nil, err
			}
			pledgePaidPercentageThisMonth := (float64(currentMonthPaidAmount) / float64(contribution.MonthlyPledgedAmount)) * 100
			contributor := ContributorDTO{
				UserID:                        contribution.ContributorUserID,
				UserName:                      "",                     // To be filled by controller
				UserPhoto:                     "",                     // To be filled by controller
				IsCreator:                     contribution.IsCreator, // This refers to whether the contributor *is* the original dream creator
				MonthlyPledgedAmount:          contribution.MonthlyPledgedAmount,
				CurrentMonthPaidAmount:        currentMonthPaidAmount,
				PledgePaidPercentageThisMonth: pledgePaidPercentageThisMonth,
			}
			contributors = append(contributors, contributor)

			// Get all transactions for the contributor for this dream
			transactions, err := s.getContributorTransactions(ctx, contribution.ContributorUserID, dreamID)
			if err != nil {
				return nil, err
			}

			// Add transaction to recent activity
			for _, transaction := range transactions {
				dreamDetails.RecentActivity = append(dreamDetails.RecentActivity, ActivityDTO{
					UserID:        contribution.ContributorUserID,
					UserName:      "", // To be filled by controller
					UserAvatarURL: "", // To be filled by controller
					Amount:        transaction.Value,
					Timestamp:     transaction.Date,
				})
			}
		}

		// Set the contributors in the dream details
		dreamDetails.Contributors = contributors

		// Order the recent activity by timestamp
		sort.Slice(dreamDetails.RecentActivity, func(i, j int) bool {
			return dreamDetails.RecentActivity[i].Timestamp.After(dreamDetails.RecentActivity[j].Timestamp)
		})
	} else {
		// For personal dreams, there are no external contributors, share links or recent activity in the same sense
		dreamDetails.Contributors = []ContributorDTO{}
		dream.ShareLinkDetails = ShareLinkDTO{}
		dreamDetails.RecentActivity = []ActivityDTO{}
	}

	dreamDetails.Dream = dream

	return dreamDetails, nil
}

// UpdateDream updates an existing dream in a dreamboard
func (s *service) UpdateDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) ([]*dreamboard.Dream, error) {
	objID, err := primitive.ObjectIDFromHex(dream.ID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dreamboard ID", errors.BadRequest, err)
	}

	// Validate dream
	if err := dream.Validate(); err != nil {
		return nil, err
	}

	// Validate that the category exists in the dreamboard
	categoryExists := false
	for _, category := range board.Categories {
		if category.Identifier == dream.Category.String() {
			categoryExists = true
			break
		}
	}
	if !categoryExists {
		return nil, errors.New(errors.Service, "category does not exist in dreamboard", errors.Validation, nil)
	}

	dream.ObjectID = objID
	dream.UpdatedAt = time.Now()

	// Recalculate the duration in months if the monthly savings cost has changed
	if dream.MonthlySavings > 0 {
		duration := int((dream.EstimatedCost - dream.CurrentRaisedAmount) / dream.MonthlySavings)
		dream.CalculatedDurationMonths = &duration
	}

	if err := s.Repository.UpdateDream(ctx, board.ObjectID, dream); err != nil {
		return nil, err
	}

	// Get updated dreamboard
	updatedBoard, err := s.Repository.Find(ctx, board.ObjectID)
	if err != nil {
		return nil, err
	}

	// Convert ObjectID to hex ID for response
	updatedBoard.ID = updatedBoard.ObjectID.Hex()

	for _, dream := range updatedBoard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Recompute totals and update the board since we updated a dream
	updatedBoard.ComputeTotals()
	if err := s.Repository.Update(ctx, updatedBoard); err != nil {
		return nil, err
	}

	return updatedBoard.Dreams, nil
}

// RemoveDream removes a dream from a dreamboard
func (s *service) RemoveDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) ([]*dreamboard.Dream, error) {
	objID, err := primitive.ObjectIDFromHex(dream.ID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dreamboard ID", errors.BadRequest, err)
	}

	dream.ObjectID = objID
	if err := s.Repository.RemoveDream(ctx, board.ObjectID, dream.ObjectID); err != nil {
		return nil, err
	}

	// Get updated dreamboard
	updatedBoard, err := s.Repository.Find(ctx, board.ObjectID)
	if err != nil {
		return nil, err
	}

	// Convert ObjectID to hex ID for response
	updatedBoard.ID = updatedBoard.ObjectID.Hex()

	for _, dream := range updatedBoard.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Recompute totals and update the board since we removed a dream
	updatedBoard.ComputeTotals()
	if err := s.Repository.Update(ctx, updatedBoard); err != nil {
		return nil, err
	}

	return updatedBoard.Dreams, nil
}

// Dream Management

// CalculateDreamDuration calculates the estimated duration in months for a dream
func (s *service) CalculateDreamDuration(ctx context.Context, dreamID string, estimatedCost monetary.Amount) (*int, error) {
	activeContributions, err := s.FindActiveContributionsByDreamID(ctx, dreamID)
	if err != nil {
		return nil, err
	}

	var totalMonthlyPledged monetary.Amount
	for _, contribution := range activeContributions {
		totalMonthlyPledged += contribution.MonthlyPledgedAmount
	}

	if totalMonthlyPledged == 0 {
		return nil, nil // Cannot calculate duration with zero contributions
	}

	duration := int(estimatedCost / totalMonthlyPledged)
	return &duration, nil
}

// Helper

// findDreamByID is a helper method to find a dream by ID across all dreamboards
func (s *service) findDreamByID(ctx context.Context, dreamID string) (*dreamboard.Dream, error) {
	// This is a simplified implementation
	// In a real scenario, we might need a more efficient way to find dreams by ID
	// across all dreamboards, possibly with a dedicated index or query

	objID, err := primitive.ObjectIDFromHex(dreamID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dream ID", errors.BadRequest, err)
	}

	// For now, we'll need to search through dreamboards
	// This is not efficient and should be optimized in production
	dreamboards, err := s.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, dreamboard := range dreamboards {
		for _, dream := range dreamboard.Dreams {
			if dream.ObjectID == objID {
				return dream, nil
			}
		}
	}

	return nil, errors.New(errors.Service, "dream not found", errors.NotFound, nil)
}

func (s *service) findDreamboardByDreamID(ctx context.Context, dreamID string) (*dreamboard.Dreamboard, error) {
	// This is a simplified implementation
	// In a real scenario, we might need a more efficient way to find dreamboards by dream ID
	// possibly with a dedicated index or query

	objID, err := primitive.ObjectIDFromHex(dreamID)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dream ID", errors.BadRequest, err)
	}

	// For now, we'll need to search through dreamboards
	// This is not efficient and should be optimized in production
	dreamboards, err := s.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, dreamboard := range dreamboards {
		for _, dream := range dreamboard.Dreams {
			if dream.ObjectID == objID {
				return dreamboard, nil
			}
		}
	}

	return nil, errors.New(errors.Service, "dreamboard not found", errors.NotFound, nil)
}

// getCurrentMonthPaidAmount returns the amount paid by a user for a dream in the current month
func (s *service) getCurrentMonthPaidAmount(ctx context.Context, userID string, dreamID string) (monetary.Amount, error) {
	// Get all transactions for the dream in the current month using the financialsheet repository
	today := time.Now()
	year := today.Year()
	month := int(today.Month())

	transactions, err := s.FinancialSheetRepository.FindAllTransactions(ctx, userID, "", year, month)
	if err != nil {
		return 0, err
	}

	// Filter only dreams transactions for a specific dream
	var dreamsTransactions []*financialsheet.Transaction
	for _, transaction := range transactions {
		if transaction.Category == financialsheet.CategoryIdentifierDreams && transaction.AttachedDreamID == dreamID {
			dreamsTransactions = append(dreamsTransactions, transaction)
		}
	}

	var currentMonthPaidAmount monetary.Amount
	for _, dreamTransaction := range dreamsTransactions {
		currentMonthPaidAmount += dreamTransaction.Value
	}

	return currentMonthPaidAmount, nil
}

// getContributorTransactions returns all transactions for a contributor for a specific dream
func (s *service) getContributorTransactions(ctx context.Context, userID string, dreamID string) ([]*financialsheet.Transaction, error) {
	// Get all transactions for the dream
	transactions, err := s.FinancialSheetRepository.FindAllTransactions(ctx, userID, "", 0, 0)
	if err != nil {
		return nil, err
	}

	// Filter only dreams transactions for a specific dream
	var dreamsTransactions []*financialsheet.Transaction
	for _, transaction := range transactions {
		if transaction.Category == financialsheet.CategoryIdentifierDreams && transaction.AttachedDreamID == dreamID {
			dreamsTransactions = append(dreamsTransactions, transaction)
		}
	}

	return dreamsTransactions, nil
}

// Helper function to get color for a category
func (s *service) getColor(category string) string {
	switch category {
	case dreamboard.Professional.Identifier:
		return dreamboard.Professional.Color
	case dreamboard.Financial.Identifier:
		return dreamboard.Financial.Color
	case dreamboard.Leisure.Identifier:
		return dreamboard.Leisure.Color
	case dreamboard.Emotional.Identifier:
		return dreamboard.Emotional.Color
	case dreamboard.Intellectual.Identifier:
		return dreamboard.Intellectual.Color
	case dreamboard.Spiritual.Identifier:
		return dreamboard.Spiritual.Color
	case dreamboard.Physical.Identifier:
		return dreamboard.Physical.Color
	case dreamboard.Intimate.Identifier:
		return dreamboard.Intimate.Color
	case dreamboard.Social.Identifier:
		return dreamboard.Social.Color
	case dreamboard.Familial.Identifier:
		return dreamboard.Familial.Color
	default:
		return dreamboard.UndefinedCategory.Color
	}
}
