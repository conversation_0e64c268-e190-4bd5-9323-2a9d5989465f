package financialsheet

import (
	"testing"
	"time"
)

func TestValidateRecurrenceMonths(t *testing.T) {
	s := &service{}
	originalDate := time.Date(2024, 3, 15, 0, 0, 0, 0, time.UTC)

	tests := []struct {
		name             string
		recurrenceMonths []int
		originalDate     time.Time
		expectError      bool
	}{
		{
			name:             "Valid recurrence months",
			recurrenceMonths: []int{4, 5, 6},
			originalDate:     originalDate,
			expectError:      false,
		},
		{
			name:             "Empty recurrence months",
			recurrenceMonths: []int{},
			originalDate:     originalDate,
			expectError:      true,
		},
		{
			name:             "Invalid month (0)",
			recurrenceMonths: []int{0, 4, 5},
			originalDate:     originalDate,
			expectError:      true,
		},
		{
			name:             "Invalid month (13)",
			recurrenceMonths: []int{4, 5, 13},
			originalDate:     originalDate,
			expectError:      true,
		},
		{
			name:             "Duplicate months",
			recurrenceMonths: []int{4, 5, 4},
			originalDate:     originalDate,
			expectError:      true,
		},
		{
			name:             "Same as original month",
			recurrenceMonths: []int{3, 4, 5}, // March is original month
			originalDate:     originalDate,
			expectError:      true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := s.validateRecurrenceMonths(tt.recurrenceMonths, tt.originalDate)
			if tt.expectError && err == nil {
				t.Errorf("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
		})
	}
}

func TestCalculateRecurrenceDate(t *testing.T) {
	s := &service{}
	originalDate := time.Date(2024, 3, 15, 10, 30, 45, 0, time.UTC)

	tests := []struct {
		name          string
		originalDate  time.Time
		targetMonth   int
		expectedYear  int
		expectedMonth int
		expectedDay   int
	}{
		{
			name:          "Future month same year",
			originalDate:  originalDate,
			targetMonth:   6, // June
			expectedYear:  2024,
			expectedMonth: 6,
			expectedDay:   15,
		},
		{
			name:          "Past month next year",
			originalDate:  originalDate,
			targetMonth:   1, // January
			expectedYear:  2025,
			expectedMonth: 1,
			expectedDay:   15,
		},
		{
			name:          "Same month next year",
			originalDate:  originalDate,
			targetMonth:   3, // March
			expectedYear:  2025,
			expectedMonth: 3,
			expectedDay:   15,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := s.calculateRecurrenceDate(tt.originalDate, tt.targetMonth)

			if result.Year() != tt.expectedYear {
				t.Errorf("Expected year %d, got %d", tt.expectedYear, result.Year())
			}
			if int(result.Month()) != tt.expectedMonth {
				t.Errorf("Expected month %d, got %d", tt.expectedMonth, int(result.Month()))
			}
			if result.Day() != tt.expectedDay {
				t.Errorf("Expected day %d, got %d", tt.expectedDay, result.Day())
			}

			// Check that time components are preserved
			if result.Hour() != originalDate.Hour() ||
				result.Minute() != originalDate.Minute() ||
				result.Second() != originalDate.Second() {
				t.Errorf("Time components not preserved")
			}
		})
	}
}

func TestCalculateRecurrenceDateEdgeCases(t *testing.T) {
	s := &service{}

	// Test February 29 (leap year) to February (non-leap year)
	originalDate := time.Date(2024, 1, 31, 0, 0, 0, 0, time.UTC) // January 31
	result := s.calculateRecurrenceDate(originalDate, 2)         // February

	// Go should automatically adjust February 31 to March 2 or 3
	if result.Month() != time.March {
		t.Errorf("Expected Go to adjust invalid date to March, got %v", result.Month())
	}
}
