package gamification

import (
	"context"
	"log"
	"sort"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/achievement"
	"github.com/dsoplabs/dinbora-backend/internal/repository/dashboard"
	_dreamboard "github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialsheet"
	_firebase "github.com/dsoplabs/dinbora-backend/internal/repository/firebase"
	_gamification "github.com/dsoplabs/dinbora-backend/internal/repository/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/repository/progression"
)

type Service interface {
	// Hero management
	DetermineVisibleHeroesForUser(ctx context.Context, userID string) ([]*gamification.Hero, error)

	// Achievement management
	FindUserAchievements(ctx context.Context, userID string) ([]*gamification.Achievement, error)
	FindContentAchievements(ctx context.Context) ([]*content.Achievement, error)

	// Event-driven achievement checking
	CheckAchievements(ctx context.Context, userID string) error

	// Specific achievement checks (can be called individually or as part of CheckAchievements)
	CheckDNAAchievement(ctx context.Context, userID string) error
	CheckExplorerAchievement(ctx context.Context, userID string) error
	CheckDreamAchievement(ctx context.Context, userID string) error
	CheckPlanningAchievement(ctx context.Context, userID string) error
	CheckInvestingAchievement(ctx context.Context, userID string) error
}

type service struct {
	Repository         _gamification.Repository
	ProgressionRepo    progression.Repository
	FinancialDNARepo   financialdna.Repository
	FinancialSheetRepo financialsheet.Repository
	DreamboardRepo     _dreamboard.Repository
	DashboardRepo      dashboard.Repository
	AchievementRepo    achievement.Repository
	FirebaseRepo       _firebase.Repository
	FirebaseApp        *firebase.App
}

func New(repository _gamification.Repository, progressionRepo progression.Repository, financialDNARepo financialdna.Repository, financialSheetRepo financialsheet.Repository, dreamboardRepo _dreamboard.Repository, dashboardRepo dashboard.Repository, achievementRepo achievement.Repository, firebaseRepo _firebase.Repository, firebaseApp *firebase.App) Service {
	return &service{
		Repository:         repository,
		ProgressionRepo:    progressionRepo,
		FinancialDNARepo:   financialDNARepo,
		FinancialSheetRepo: financialSheetRepo,
		DreamboardRepo:     dreamboardRepo,
		DashboardRepo:      dashboardRepo,
		AchievementRepo:    achievementRepo,
		FirebaseRepo:       firebaseRepo,
		FirebaseApp:        firebaseApp,
	}
}

const (
	dnaAchievementIdentifier       = "DNA"
	explorerAchievementIdentifier  = "explorador"
	dreamAchievementIdentifier     = "sonhos"
	planningAchievementIdentifier  = "apontamento-financeiro"
	investingAchievementIdentifier = "simulador"
)

const (
	dnaChallengePhase001Identifier       = "perfil-financeiro"
	explorerChallengePhase001Identifier  = "diagnostico-inicial-phase001"
	explorerChallengePhase002Identifier  = "calcule-sua-reserva-phase001"
	explorerChallengePhase003Identifier  = "apontamento-financeiro-phase001"
	dreamChallengePhase001Identifier     = "importancia-dos-sonhos-phase001"
	planningChallengePhase001Identifier  = "plano-financeiro-phase001"
	investingChallengePhase001Identifier = "simulacao-independencia-phase001"
)

// Hero management

// DetermineVisibleHeroesForUser applies business logic to find all heroes
func (s *service) DetermineVisibleHeroesForUser(ctx context.Context, userID string) ([]*gamification.Hero, error) {
	// Use a map to collect heroes. The key is the hero's key, which prevents duplicates.
	visibleHeroes := make(map[string]*gamification.Hero)

	// Helper function to reduce repetition. This is optional but can be nice.
	addHero := func(key string) {
		// Using MustGetHero ensures that if a hero is misconfigured, we find out immediately.
		hero := gamification.MustGetHero(key) // Assuming this returns *Hero and panics if not found
		visibleHeroes[hero.Key] = hero
	}

	// Step 1: Add heroes that are always visible to the user
	// Apontamento
	addHero(gamification.HeroFinancialNote) // Assuming this returns *Hero and panics if not found
	// Sonhos
	addHero(gamification.HeroDreamBoard) // Assuming this returns *Hero and panics if not found

	// Step 2: Add heroes that are visible based on user achievements
	// Meu DNA
	hasCompletedChallenge, err := s.hasCompletedPerfilFinanceiroChallenge(ctx, userID)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to check perfil-financeiro challenge completion", errors.Internal, err)
	}
	if hasCompletedChallenge {
		// Challenge completed, add Meu DNA hero
		addHero(gamification.HeroFinancialTree) // Assuming this returns *Hero and panics if not found
	}

	// Planejamento
	hasCompletedChallenge, err = s.hasCompletedPlanoFinanceiroChallenge(ctx, userID)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to check apontamento financeiro challenge completion", errors.Internal, err)
	}
	if hasCompletedChallenge {
		// Challenge completed, add Planejamento hero
		addHero(gamification.HeroFinancialPlanning) // Assuming this returns *Hero and panics if not found
	}

	// Dinbora+
	// TODO: Implement this check
	// hasDinboraPlusPlan, err := s.hasDinboraPlusPlan(ctx, userID)
	// if err != nil {
	// 	return nil, errors.New(errors.Service, "failed to check if user has dinbora plus plan", errors.Internal, err)
	// }
	// if hasDinboraPlusPlan {
	// 	// User has the Dinbora+ plan, add Dinbora+ hero
	// 	addHero(gamification.HeroDinboraPlus) // Assuming this returns *Hero and panics if not found
	// }

	// Step 3: Convert the map to a slice and sort it by order
	result := make([]*gamification.Hero, 0, len(visibleHeroes))
	for _, hero := range visibleHeroes {
		result = append(result, hero)
	}
	// Sort the slice by order
	sort.Slice(result, func(i, j int) bool {
		return result[i].Order < result[j].Order
	})

	return result, nil
}

// Achievement management
func (s *service) FindUserAchievements(ctx context.Context, userID string) ([]*gamification.Achievement, error) {
	// Check for the user achievements to guarantee that old users will have their achievements created
	// after the gamification service is implemented
	if err := s.CheckAchievements(ctx, userID); err != nil {
		// Log error but don't fail the entire check process
		// This allows other achievements to be checked even if one fails
		log.Printf("Failed to check achievements for user %s: %v", userID, err)
	}

	userAchievements, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		// Check if it's a not found error
		if domainErr, ok := err.(*errors.DomainError); ok && domainErr.Kind() == errors.NotFound {
			// User has no achievements yet, return empty slice
			return []*gamification.Achievement{}, nil
		}
		return nil, errors.New(errors.Service, "failed to get user achievements", errors.Internal, err)
	}

	return userAchievements.Achievements, nil
}

func (s *service) FindContentAchievements(ctx context.Context) ([]*content.Achievement, error) {
	contentAchievements, err := s.AchievementRepo.FindAll(ctx)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to get content achievements", errors.Internal, err)
	}

	return contentAchievements, nil
}

// Event-driven achievement checking
func (s *service) CheckAchievements(ctx context.Context, userID string) error {
	// Check all available achievements
	// Descobriu o DNA
	if err := s.CheckDNAAchievement(ctx, userID); err != nil {
		// Log error but don't fail the entire check process
		// This allows other achievements to be checked even if one fails
		log.Printf("Failed to check dna achievement for user %s: %v", userID, err)
	}

	// Explorou Oportunidades
	if err := s.CheckExplorerAchievement(ctx, userID); err != nil {
		// Log error but don't fail the entire check process
		// This allows other achievements to be checked even if one fails
		log.Printf("Failed to check explorer achievement for user %s: %v", userID, err)
	}

	// Semeando Sonhos
	if err := s.CheckDreamAchievement(ctx, userID); err != nil {
		// Log error but don't fail the entire check process
		// This allows other achievements to be checked even if one fails
		log.Printf("Failed to check dream achievement for user %s: %v", userID, err)
	}

	// Planejou o Futuro
	if err := s.CheckPlanningAchievement(ctx, userID); err != nil {
		// Log error but don't fail the entire check process
		// This allows other achievements to be checked even if one fails
		log.Printf("Failed to check planning achievement for user %s: %v", userID, err)
	}

	// Começou a Investir
	if err := s.CheckInvestingAchievement(ctx, userID); err != nil {
		// Log error but don't fail the entire check process
		// This allows other achievements to be checked even if one fails
		log.Printf("Failed to check investing achievement for user %s: %v", userID, err)
	}

	return nil
}

func (s *service) awardAchievement(ctx context.Context, userID, achievementIdentifier string) error {
	// Create the achievement for the user's achievement document
	err := s.Repository.CreateAchievement(ctx, userID, achievementIdentifier)
	if err != nil {
		return errors.New(errors.Service, "failed to award achievement", errors.Internal, err)
	}

	log.Printf("Awarded %s achievement to user %s", achievementIdentifier, userID)

	// Send Firebase Notification
	// Start this in a new goroutine so it doesn't block the main API response
	go func() {
		// Create a background context for the goroutine
		bgCtx := context.Background()

		err := s.sendAwardNotification(bgCtx, userID, achievementIdentifier)
		if err != nil {
			log.Printf("Failed to send award notification for user %s: %v", userID, err)
		}
	}()

	return nil
}

func (s *service) sendAwardNotification(ctx context.Context, userID, achievementIdentifier string) error {
	// Get the user's FCM token
	fcmToken, err := s.FirebaseRepo.FindByUserID(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to find user fcm token", errors.Internal, err)
	}

	// Get the achievement details
	achievement, err := s.AchievementRepo.FindByIdentifier(ctx, achievementIdentifier)
	if err != nil {
		return errors.New(errors.Service, "failed to find achievement", errors.Internal, err)
	}

	// Get the FCM client
	fcmClient, err := s.FirebaseApp.Messaging(ctx)
	if err != nil {
		return errors.New(errors.Service, "failed to get firebase client", errors.Internal, err)
	}

	// Construct a DATA-ONLY message
	message := &messaging.Message{
		Data: map[string]string{
			"action":                 "open_achievement_modal", // Critical for the frontend to identify the event
			"achievementIdentifier":  achievementIdentifier,
			"achievementTitle":       achievement.Title,
			"achievementName":        achievement.Name,
			"achievementDescription": achievement.Description,
			"achievementImage":       achievement.Logo,
		},
		Token: fcmToken.FCMToken, // The specific device token for the user
	}

	// Capture both the messageID string and the error.
	messageID, err := fcmClient.Send(ctx, message)
	if err != nil {
		// (Your intelligent error handling for unregistered tokens would go here)
		if messaging.IsUnregistered(err) {
			log.Printf("FCM token for user %s is unregistered. Deleting.", userID)
			s.FirebaseRepo.Delete(ctx, userID)
			return nil
		}
		return errors.New(errors.Service, "failed to send firebase message", errors.Internal, err)
	}

	// If there was no error, log the success with the message ID.
	// This is invaluable for debugging later.
	log.Printf(
		"Successfully sent award notification to user %s. FCM Message ID: %s",
		userID,
		messageID,
	)

	return nil
}
