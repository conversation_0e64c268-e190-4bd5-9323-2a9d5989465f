package dreamboard

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// CreateDreamResponseDTO represents the response when creating a dream
type CreateDreamResponseDTO struct {
	ID                       string                        `json:"id,omitempty"`
	Category                 dreamboard.CategoryIdentifier `json:"category"`
	Color                    string                        `json:"color"`
	Title                    string                        `json:"title"`
	TimeFrame                dreamboard.TimeFrame          `json:"timeFrame"`
	Deadline                 time.Time                     `json:"deadline"`
	EstimatedCost            monetary.Amount               `json:"estimatedCost"`
	MonthlySavings           monetary.Amount               `json:"monthlySavings"`
	MoneySource              []dreamboard.MoneySource      `json:"moneySource"`
	Completed                bool                          `json:"completed"`
	CustomMoneySource        string                        `json:"customMoneySource,omitempty"`
	CreatedAt                time.Time                     `json:"createdAt"`
	UpdatedAt                time.Time                     `json:"updatedAt"`
	IsShared                 bool                          `json:"isShared"`
	CreatorUserID            string                        `json:"creatorUserId"`
	CurrentRaisedAmount      monetary.Amount               `json:"currentRaisedAmount"`
	FundingStatus            dreamboard.FundingStatus      `json:"fundingStatus"`
	CalculatedDurationMonths *int                          `json:"calculatedDurationMonths,omitempty"`
	Code                     *string                       `json:"code,omitempty"`
}

// DreamDetails contains comprehensive information about a dream
type DreamDetails struct {
	Dream          DreamDTO         `json:"dream"`
	Contributors   []ContributorDTO `json:"contributors"`
	RecentActivity []ActivityDTO    `json:"recentActivity"`
}

// DreamDTO contains basic information about a dream
type DreamDTO struct {
	ID                      string          `json:"id"`
	Title                   string          `json:"title"`
	IsCreator               bool            `json:"isCreator"`
	TotalCost               monetary.Amount `json:"totalCost"`
	RaisedAmount            monetary.Amount `json:"raisedAmount"`
	RemainingAmount         monetary.Amount `json:"remainingAmount"`
	EstimatedDurationMonths *int            `json:"estimatedDurationMonths"`
	FundingStatus           string          `json:"fundingStatus"`
	ShareLinkDetails        ShareLinkDTO    `json:"shareLinkDetails"`
}

// InviteDetailsDTO contains information about a shared dream invitation
type InviteDetailsDTO struct {
	DreamID                  string          `json:"dreamId"`
	DreamTitle               string          `json:"dreamTitle"`
	DreamDescription         string          `json:"dreamDescription,omitempty"`
	CreatorUserID            string          `json:"creatorUserId"`
	CreatorUserName          string          `json:"creatorUserName"`
	CreatorPhotoURL          string          `json:"creatorAvatarUrl"`
	EstimatedCost            monetary.Amount `json:"estimatedCost"`
	CalculatedDurationMonths *int            `json:"calculatedDurationMonths"`
	CurrentRaisedAmount      monetary.Amount `json:"currentRaisedAmount"`
	ParticipantCount         int             `json:"participantCount"`
	IsExpired                bool            `json:"isExpired"`
	IsEnabled                bool            `json:"isEnabled"`
}

// ShareLinkDTO contains information about a dream's share link
type ShareLinkDTO struct {
	Code      string `json:"code"`
	IsEnabled bool   `json:"isEnabled"`
}

// ContributorDTO contains information about a dream contributor
type ContributorDTO struct {
	UserID                        string          `json:"userId"`
	UserName                      string          `json:"userName"`
	UserPhoto                     string          `json:"userPhoto"`
	IsCreator                     bool            `json:"isCreator"`
	MonthlyPledgedAmount          monetary.Amount `json:"monthlyPledgedAmount"`
	CurrentMonthPaidAmount        monetary.Amount `json:"currentMonthPaidAmount"`
	PledgePaidPercentageThisMonth float64         `json:"pledgePaidPercentageThisMonth"`
}

// ActivityDTO represents an activity entry in the dream dashboard
type ActivityDTO struct {
	UserID        string          `json:"userId"`
	UserName      string          `json:"userName"`
	UserAvatarURL string          `json:"userAvatarUrl"`
	Amount        monetary.Amount `json:"amount,omitempty"`
	Timestamp     time.Time       `json:"date"`
}
