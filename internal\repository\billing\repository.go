package billing

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// PlanReader defines read operations for plans
type PlanReader interface {
	Find(ctx context.Context, id primitive.ObjectID) (*billing.Plan, error)
	FindByHotmartProductID(ctx context.Context, hotmartProductID string) (*billing.Plan, error)
	FindByAppleProductID(ctx context.Context, appleProductID string) (*billing.Plan, error)
	FindAll(ctx context.Context) ([]*billing.Plan, error)
	FindActive(ctx context.Context) ([]*billing.Plan, error)
}

// PlanWriter defines write operations for plans
type PlanWriter interface {
	Create(ctx context.Context, plan *billing.Plan) (string, error)
	Update(ctx context.Context, plan *billing.Plan) error
	Delete(ctx context.Context, id primitive.ObjectID) error
}

// PlanRepository combines plan read and write operations
type PlanRepository interface {
	PlanReader
	PlanWriter
}

// SubscriptionReader defines read operations for subscriptions
type SubscriptionReader interface {
	Find(ctx context.Context, id primitive.ObjectID) (*billing.Subscription, error)
	FindByUser(ctx context.Context, userID primitive.ObjectID) ([]*billing.Subscription, error)
	FindActiveByUser(ctx context.Context, userID primitive.ObjectID) ([]*billing.Subscription, error)
	FindByProviderSubscriptionID(ctx context.Context, provider billing.PaymentProvider, providerSubscriptionID string) (*billing.Subscription, error)
	FindExpiring(ctx context.Context, days int) ([]*billing.Subscription, error)
	FindByStatus(ctx context.Context, status billing.SubscriptionStatus) ([]*billing.Subscription, error)
}

// SubscriptionWriter defines write operations for subscriptions
type SubscriptionWriter interface {
	Create(ctx context.Context, subscription *billing.Subscription) (string, error)
	Update(ctx context.Context, subscription *billing.Subscription) error
	Delete(ctx context.Context, id primitive.ObjectID) error
}

// SubscriptionRepository combines subscription read and write operations
type SubscriptionRepository interface {
	SubscriptionReader
	SubscriptionWriter
}

// PaymentReader defines read operations for payments
type PaymentReader interface {
	Find(ctx context.Context, id primitive.ObjectID) (*billing.Payment, error)
	FindByUser(ctx context.Context, userID primitive.ObjectID) ([]*billing.Payment, error)
	FindBySubscription(ctx context.Context, subscriptionID primitive.ObjectID) ([]*billing.Payment, error)
	FindByProviderTransactionID(ctx context.Context, provider billing.PaymentProvider, providerTransactionID string) (*billing.Payment, error)
	FindByStatus(ctx context.Context, status billing.PaymentStatus) ([]*billing.Payment, error)
	FindByDateRange(ctx context.Context, startDate, endDate string) ([]*billing.Payment, error)
}

// PaymentWriter defines write operations for payments
type PaymentWriter interface {
	Create(ctx context.Context, payment *billing.Payment) (string, error)
	Update(ctx context.Context, payment *billing.Payment) error
	Delete(ctx context.Context, id primitive.ObjectID) error
}

// PaymentRepository combines payment read and write operations
type PaymentRepository interface {
	PaymentReader
	PaymentWriter
}

// Repository combines all billing repositories
type Repository interface {
	Plans() PlanRepository
	Subscriptions() SubscriptionRepository
	Payments() PaymentRepository
}
