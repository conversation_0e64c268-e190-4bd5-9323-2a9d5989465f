package achievement

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.ACHIEVEMENTS_COLLECTION),
	}

	// Create new index
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "requirement", Value: 1}},
			Options: options.Index().SetUnique(true).SetName("requirement"),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on achievement.requirement field")
		db.Client().Disconnect(context.Background())
	}

	return repo
}

func (m mongoDB) Create(ctx context.Context, achievement *content.Achievement) error {
	_, err := m.collection.InsertOne(ctx, achievement)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "achievement already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create achievement", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) Find(ctx context.Context, id string) (*content.Achievement, error) {
	objectId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid ID format", errors.Validation, err)
	}

	var achievement content.Achievement
	if err = m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectId}}).Decode(&achievement); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "achievement not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find achievement", errors.Internal, err)
	}

	return &achievement, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*content.Achievement, error) {
	cursor, err := m.collection.Find(ctx, bson.D{}, nil)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query achievements", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var achievements []*content.Achievement
	for cursor.Next(ctx) {
		var achievement content.Achievement
		if err = cursor.Decode(&achievement); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode achievement", errors.Internal, err)
		}
		achievements = append(achievements, &achievement)
	}

	return achievements, nil
}

func (m mongoDB) FindByIdentifier(ctx context.Context, identifier string) (*content.Achievement, error) {
	var achievement content.Achievement
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "identifier", Value: identifier}}).Decode(&achievement); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "achievement not found by identifier", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find achievement by identifier", errors.Internal, err)
	}

	return &achievement, nil
}

func (m mongoDB) Update(ctx context.Context, achievement *content.Achievement) error {
	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: achievement.ObjectID}},
		primitive.M{"$set": achievement})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "achievement update would cause conflict", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update achievement", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "achievement not found for update", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid ID format", errors.Validation, err)
	}

	result, err := m.collection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectID}})
	if err != nil {
		return err
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "achievement not found for deletion", errors.NotFound, nil)
	}

	return nil
}
