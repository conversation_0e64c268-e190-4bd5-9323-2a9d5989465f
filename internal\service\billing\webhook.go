package billing

import (
	"context"
	"encoding/json"
	"log"
	"strconv"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing/hotmart"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ApplePayWebhookData represents the structure of Apple Pay webhook data
type ApplePayWebhookData struct {
	NotificationType      string `json:"notificationType"`
	TransactionID         string `json:"transactionId"`
	OriginalTransactionID string `json:"originalTransactionId"`
	ProductID             string `json:"productId"`
	PurchaseDate          string `json:"purchaseDate"`
	ExpiresDate           string `json:"expiresDate"`
	Environment           string `json:"environment"`
	Status                string `json:"status"`
	Amount                int    `json:"amount"`
	Currency              string `json:"currency"`
}

// Hotmart webhook handlers
func (s *service) ProcessHotmartWebhook(ctx context.Context, webhookData interface{}) error {
	// Convert webhook data to HotmartWebhookData
	body, err := json.Marshal(webhookData)
	if err != nil {
		return errors.New(errors.Service, "erro ao serializar webhookData", errors.Internal, err)
	}

	// 2. Deserializa para um mapa genérico
	var rawMap map[string]interface{}
	if err := json.Unmarshal(body, &rawMap); err != nil {
		log.Print("erro")
	}

	// 3. Extrai campos básicos
	id, _ := rawMap["id"].(string)
	event, _ := rawMap["event"].(string)
	version, _ := rawMap["version"].(string)

	var creationDate int64
	if cd, ok := rawMap["creation_date"].(float64); ok {
		creationDate = int64(cd)
	}

	// 4. Extrai o campo "data" e reserializa ele separadamente
	dataObj, ok := rawMap["data"]
	if !ok {
		return errors.New(errors.Service, "campo 'data' ausente", errors.Validation, nil)
	}
	dataBytes, err := json.Marshal(dataObj)
	if err != nil {
		return errors.New(errors.Service, "erro ao serializar campo 'data'", errors.Internal, err)
	}

	// 5. Monta a struct do envelope corretamente
	hotmartData := hotmart.Envelope{
		ID:           id,
		Event:        event,
		Version:      version,
		CreationDate: creationDate,
		Data:         dataBytes, // <- json.RawMessage = []byte
	}

	// Route to appropriate handler based on event type
	switch hotmartData.Event {
	case "PURCHASE_APPROVED", "PURCHASE_BILLET_PRINTED":
		var payload hotmart.PurchaseData
		if err := json.Unmarshal(hotmartData.Data, &payload); err != nil {
			return errors.New(errors.Service, "failed to parse PURCHASE_APPROVED data", errors.Validation, err)
		}
		return s.HandleHotmartSubscriptionCreated(ctx, payload)
	case "SUBSCRIPTION_CANCELLATION":
		var payload hotmart.SubscriptionCancellationData
		if err := json.Unmarshal(hotmartData.Data, &payload); err != nil {
			return errors.New(errors.Service, "failed to parse PURCHASE_APPROVED data", errors.Validation, err)
		}
		return s.HandleHotmartSubscriptionCancelled(ctx, &payload)
	case "PURCHASE_COMPLETED":
		var payload hotmart.PurchaseData
		if err := json.Unmarshal(hotmartData.Data, &payload); err != nil {
			return errors.New(errors.Service, "failed to parse PURCHASE_APPROVED data", errors.Validation, err)
		}
		return s.HandleHotmartPaymentCompleted(ctx, &payload)
	case "PURCHASE_EXPIRED", "PURCHASE_REFUNDED", "PURCHASE_PROTEST", "PURCHASE_CANCELED":
		var payload hotmart.PurchaseData
		if err := json.Unmarshal(hotmartData.Data, &payload); err != nil {
			return errors.New(errors.Service, "failed to parse PURCHASE_APPROVED data", errors.Validation, err)
		}
		return s.HandleHotmartPaymentRefunded(ctx, &payload)
	default:
		print("Unknow event: ", hotmartData.Event)
		return nil
	}
}

func (s *service) HandleHotmartSubscriptionCreated(ctx context.Context, hotmartData hotmart.PurchaseData) error {

	// Validate required webhook data
	if hotmartData.Buyer.Email == "" {
		return errors.New(errors.Service, "customer email is required in webhook data", errors.Validation, nil)
	}
	if hotmartData.Product.ID == 0 {
		return errors.New(errors.Service, "product ID is required in webhook data", errors.Validation, nil)
	}
	if hotmartData.Subscription.Subscriber.Code == "" {
		return errors.New(errors.Service, "product ID is required in webhook data", errors.Validation, nil)
	}

	// Find plan by Hotmart product ID
	plan, err := s.repo.Plans().FindByHotmartProductID(ctx, strconv.Itoa(hotmartData.Product.ID))
	if err != nil {
		return errors.New(errors.Service, "plan not found for hotmart product", errors.NotFound, err)
	}

	// Find user by email from webhook data
	userID, err := s.findUserByEmail(ctx, hotmartData.Buyer.Email)
	if err != nil {
		// Se não encontrar, cria novo usuário
		newUser := &model.User{
			Name:           hotmartData.Buyer.FirstName,
			LastName:       hotmartData.Buyer.LastName,
			Email:          hotmartData.Buyer.Email,
			Phone:          hotmartData.Buyer.CheckoutPhoneCode + hotmartData.Buyer.CheckoutPhone,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
			RegisterSource: "hotmart",
		}

		if err := s.userService.CreateNewUser(ctx, newUser, ""); err != nil {
			return errors.New(errors.Service, "failed to create user from webhook", errors.Internal, err)
		}

		userID, err = primitive.ObjectIDFromHex(newUser.ID)
		if err != nil {
			return errors.New(errors.Service, "failed to parse new user ID", errors.Internal, err)
		}
	}

	// Check if subscription already exists for this provider subscription ID
	existingSubscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderHotmart, hotmartData.Subscription.Subscriber.Code)
	if err == nil && existingSubscription != nil {
		return errors.New(errors.Service, "Subscription already exists", errors.Validation, nil)
	}

	subscription, err := s.CreateSubscription(ctx, userID, plan.ObjectID, billing.PaymentProviderHotmart)
	if err != nil {
		return err
	}

	// Set provider subscription ID
	subscription.ProviderSubscriptionID = hotmartData.Subscription.Subscriber.Code
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) HandleHotmartSubscriptionCancelled(ctx context.Context, webhookData interface{}) error {
	hotmartData, ok := webhookData.(*hotmart.SubscriptionCancellationData)
	if !ok {
		return errors.New(errors.Service, "invalid hotmart webhook data", errors.Validation, nil)
	}

	// Find subscription by provider subscription ID
	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderHotmart, hotmartData.Subscriber.Code)
	if err != nil {
		return err
	}

	// Cancel subscription
	return s.CancelSubscription(ctx, subscription.ObjectID, "Cancelled via Hotmart webhook")
}

func (s *service) HandleHotmartPaymentCompleted(ctx context.Context, webhookData interface{}) error {

	hotmartData, ok := webhookData.(*hotmart.PurchaseData)
	if !ok {
		return errors.New(errors.Service, "invalid hotmart webhook data", errors.Validation, nil)
	}

	// Find subscription by provider subscription ID
	var subscriptionID *primitive.ObjectID
	if hotmartData.Subscription.Subscriber.Code != "" {
		subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderHotmart, hotmartData.Subscription.Subscriber.Code)
		if err == nil {
			subscriptionID = &subscription.ObjectID
		}
	}

	// Find user by email from webhook data
	userID, err := s.findUserByEmail(ctx, hotmartData.Buyer.Email)
	if err != nil {
		return errors.New(errors.Service, "user not found for email", errors.NotFound, err)
	}

	price, err := hotmartData.Purchase.Price.Value.Int64()
	if err != nil {
		return err
	}
	payment, err := s.ProcessPayment(ctx, userID, subscriptionID, billing.PaymentProviderHotmart, hotmartData.Purchase.Transaction, price, hotmartData.Purchase.FullPrice.CurrencyValue, hotmartData)
	if err != nil {
		return err
	}

	// Mark payment as completed
	return s.MarkPaymentCompleted(ctx, payment.ObjectID)
}

func (s *service) HandleHotmartPaymentRefunded(ctx context.Context, webhookData interface{}) error {
	hotmartData, ok := webhookData.(*hotmart.PurchaseData)
	if !ok {
		return errors.New(errors.Service, "invalid hotmart webhook data", errors.Validation, nil)
	}

	// Find payment by provider transaction ID
	payment, err := s.repo.Payments().FindByProviderTransactionID(ctx, billing.PaymentProviderHotmart, hotmartData.Purchase.Transaction)
	if err != nil {
		return err
	}

	// Mark payment as refunded
	return s.MarkPaymentRefunded(ctx, payment.ObjectID)
}

// Helper method to lookup user by email for webhook processing
func (s *service) findUserByEmail(ctx context.Context, email string) (primitive.ObjectID, error) {
	user, err := s.userRepository.FindByEmail(ctx, email)
	if err != nil {
		return primitive.NilObjectID, err
	}

	userID, err := primitive.ObjectIDFromHex(user.ID)
	if err != nil {
		return primitive.NilObjectID, err
	}

	return userID, nil
}

// Apple Pay webhook handlers
func (s *service) ProcessApplePayWebhook(ctx context.Context, webhookData interface{}) error {
	// Convert webhook data to ApplePayWebhookData
	dataBytes, err := json.Marshal(webhookData)
	if err != nil {
		return errors.New(errors.Service, "failed to marshal apple pay webhook data", errors.Internal, err)
	}

	var appleData ApplePayWebhookData
	if err := json.Unmarshal(dataBytes, &appleData); err != nil {
		return errors.New(errors.Service, "failed to unmarshal apple pay webhook data", errors.Validation, err)
	}

	// Route to appropriate handler based on notification type
	switch appleData.NotificationType {
	case "INITIAL_BUY", "DID_RENEW":
		return s.HandleApplePaySubscriptionCreated(ctx, appleData)
	case "CANCEL", "DID_FAIL_TO_RENEW":
		return s.HandleApplePaySubscriptionCancelled(ctx, appleData)
	case "INTERACTIVE_RENEWAL":
		return s.HandleApplePayPaymentCompleted(ctx, appleData)
	case "REFUND":
		return s.HandleApplePayPaymentRefunded(ctx, appleData)
	default:
		// Log unknown notification type but don't fail
		return nil
	}
}

func (s *service) HandleApplePaySubscriptionCreated(ctx context.Context, webhookData interface{}) error {
	// For Apple Pay, we need to handle user identification differently
	// since Apple Pay webhooks don't include user email.
	// This would typically require additional Apple Pay integration
	// or finding existing user by transaction ID.
	// For now, we'll return an error indicating this needs proper implementation.
	return errors.New(errors.Service, "Apple Pay subscription creation requires additional user identification logic", errors.NotImplemented, nil)
}

func (s *service) HandleApplePaySubscriptionCancelled(ctx context.Context, webhookData interface{}) error {
	appleData, ok := webhookData.(ApplePayWebhookData)
	if !ok {
		return errors.New(errors.Service, "invalid apple pay webhook data", errors.Validation, nil)
	}

	// Find subscription by provider subscription ID
	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderApplePay, appleData.OriginalTransactionID)
	if err != nil {
		return err
	}

	// Cancel subscription
	return s.CancelSubscription(ctx, subscription.ObjectID, "Cancelled via Apple Pay webhook")
}

func (s *service) HandleApplePayPaymentCompleted(ctx context.Context, webhookData interface{}) error {
	appleData, ok := webhookData.(ApplePayWebhookData)
	if !ok {
		return errors.New(errors.Service, "invalid apple pay webhook data", errors.Validation, nil)
	}

	// Find subscription by provider subscription ID
	var subscriptionID *primitive.ObjectID
	if appleData.OriginalTransactionID != "" {
		subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderApplePay, appleData.OriginalTransactionID)
		if err == nil {
			subscriptionID = &subscription.ObjectID
		}
	}

	// For Apple Pay payments, we need to find the user through the subscription
	// since Apple Pay webhooks don't include user email directly
	var userID primitive.ObjectID
	if subscriptionID != nil {
		subscription, err := s.repo.Subscriptions().Find(ctx, *subscriptionID)
		if err != nil {
			return errors.New(errors.Service, "subscription not found for Apple Pay payment", errors.NotFound, err)
		}
		userID = subscription.UserID
	} else {
		return errors.New(errors.Service, "Apple Pay payment requires existing subscription for user identification", errors.Validation, nil)
	}
	payment, err := s.ProcessPayment(ctx, userID, subscriptionID, billing.PaymentProviderApplePay, appleData.TransactionID, int64(appleData.Amount), appleData.Currency, appleData)
	if err != nil {
		return err
	}

	// Mark payment as completed
	return s.MarkPaymentCompleted(ctx, payment.ObjectID)
}

func (s *service) HandleApplePayPaymentRefunded(ctx context.Context, webhookData interface{}) error {
	appleData, ok := webhookData.(ApplePayWebhookData)
	if !ok {
		return errors.New(errors.Service, "invalid apple pay webhook data", errors.Validation, nil)
	}

	// Find payment by provider transaction ID
	payment, err := s.repo.Payments().FindByProviderTransactionID(ctx, billing.PaymentProviderApplePay, appleData.TransactionID)
	if err != nil {
		return err
	}

	// Mark payment as refunded
	return s.MarkPaymentRefunded(ctx, payment.ObjectID)
}
