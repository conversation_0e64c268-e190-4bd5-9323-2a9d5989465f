package billing

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Payment CRUD operations
func (s *service) CreatePayment(ctx context.Context, payment *billing.Payment) (string, error) {
	payment.SetDefaults()

	if err := payment.Validate(); err != nil {
		return "", err
	}

	return s.repo.Payments().Create(ctx, payment)
}

func (s *service) FindPayment(ctx context.Context, id primitive.ObjectID) (*billing.Payment, error) {
	return s.repo.Payments().Find(ctx, id)
}

func (s *service) FindUserPayments(ctx context.Context, userID primitive.ObjectID) ([]*billing.Payment, error) {
	return s.repo.Payments().FindByUser(ctx, userID)
}

func (s *service) FindSubscriptionPayments(ctx context.Context, subscriptionID primitive.ObjectID) ([]*billing.Payment, error) {
	return s.repo.Payments().FindBySubscription(ctx, subscriptionID)
}

func (s *service) UpdatePayment(ctx context.Context, payment *billing.Payment) error {
	return s.repo.Payments().Update(ctx, payment)
}

// Payment processing operations
func (s *service) ProcessPayment(ctx context.Context, userID primitive.ObjectID, subscriptionID *primitive.ObjectID, provider billing.PaymentProvider, providerTransactionID string, amount int64, currency string, webhookData interface{}) (*billing.Payment, error) {
	// Check if payment already exists (webhook deduplication)
	existingPayment, err := s.repo.Payments().FindByProviderTransactionID(ctx, provider, providerTransactionID)
	if err == nil && existingPayment != nil {
		// Payment already exists, return it
		return existingPayment, nil
	}

	// Create new payment
	payment := &billing.Payment{
		UserID:                userID,
		SubscriptionID:        subscriptionID,
		Provider:              provider,
		ProviderTransactionID: providerTransactionID,
		Amount:                monetary.Amount(amount),
		Currency:              currency,
		Status:                billing.PaymentStatusPending,
		Type:                  billing.PaymentTypeSubscription,
	}

	// Set webhook data
	if webhookData != nil {
		if err := payment.SetWebhookData(webhookData); err != nil {
			return nil, errors.New(errors.Service, "failed to set webhook data", errors.Internal, err)
		}
	}

	// Determine payment type based on subscription
	if subscriptionID != nil {
		subscription, err := s.repo.Subscriptions().Find(ctx, *subscriptionID)
		if err == nil && subscription != nil {
			// Check if this is a renewal payment
			payments, err := s.repo.Payments().FindBySubscription(ctx, *subscriptionID)
			if err == nil && len(payments) > 0 {
				payment.Type = billing.PaymentTypeRenewal
			}
		}
	}

	paymentID, err := s.repo.Payments().Create(ctx, payment)
	if err != nil {
		return nil, err
	}

	// Convert string ID back to ObjectID
	objectID, err := primitive.ObjectIDFromHex(paymentID)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to convert payment ID", errors.Internal, err)
	}

	payment.ObjectID = objectID
	payment.ID = paymentID

	return payment, nil
}

func (s *service) MarkPaymentCompleted(ctx context.Context, paymentID primitive.ObjectID) error {
	payment, err := s.repo.Payments().Find(ctx, paymentID)
	if err != nil {
		return err
	}

	if payment.IsCompleted() {
		return nil // Already completed
	}

	if billing.PaymentHelperInstance.IsPaymentFinal(payment.Status) && !payment.IsPending() {
		return errors.New(errors.Service, "cannot mark final payment as completed", errors.Validation, nil)
	}

	payment.MarkAsCompleted()

	// Update subscription status if this is a subscription payment
	if payment.SubscriptionID != nil {
		if err := s.handleSuccessfulPayment(ctx, *payment.SubscriptionID, payment); err != nil {
			// Log error but don't fail the payment completion
			// The payment is still valid even if subscription update fails
		}
	}

	return s.repo.Payments().Update(ctx, payment)
}

func (s *service) MarkPaymentFailed(ctx context.Context, paymentID primitive.ObjectID, reason string) error {
	payment, err := s.repo.Payments().Find(ctx, paymentID)
	if err != nil {
		return err
	}

	if payment.IsFailed() {
		return nil // Already failed
	}

	if billing.PaymentHelperInstance.IsPaymentFinal(payment.Status) && !payment.IsPending() {
		return errors.New(errors.Service, "cannot mark final payment as failed", errors.Validation, nil)
	}

	payment.MarkAsFailed(reason)

	// Handle failed payment for subscription
	if payment.SubscriptionID != nil {
		if err := s.handleFailedPayment(ctx, *payment.SubscriptionID, payment); err != nil {
			// Log error but don't fail the payment update
		}
	}

	return s.repo.Payments().Update(ctx, payment)
}

func (s *service) MarkPaymentRefunded(ctx context.Context, paymentID primitive.ObjectID) error {
	payment, err := s.repo.Payments().Find(ctx, paymentID)
	if err != nil {
		return err
	}

	if payment.IsRefunded() {
		return nil // Already refunded
	}

	if !payment.IsCompleted() {
		return errors.New(errors.Service, "can only refund completed payments", errors.Validation, nil)
	}

	payment.MarkAsRefunded()

	// Handle refund for subscription
	if payment.SubscriptionID != nil {
		if err := s.handleRefundedPayment(ctx, *payment.SubscriptionID, payment); err != nil {
			// Log error but don't fail the payment update
		}
	}

	return s.repo.Payments().Update(ctx, payment)
}

// Payment lookup operations
func (s *service) FindPaymentByProviderTransactionID(ctx context.Context, provider billing.PaymentProvider, providerTransactionID string) (*billing.Payment, error) {
	if providerTransactionID == "" {
		return nil, errors.New(errors.Service, "provider transaction ID is required", errors.Validation, nil)
	}

	return s.repo.Payments().FindByProviderTransactionID(ctx, provider, providerTransactionID)
}

func (s *service) FindPaymentsByDateRange(ctx context.Context, startDate, endDate string) ([]*billing.Payment, error) {
	return s.repo.Payments().FindByDateRange(ctx, startDate, endDate)
}

// Helper methods for payment processing

// handleSuccessfulPayment processes a successful payment for a subscription
func (s *service) handleSuccessfulPayment(ctx context.Context, subscriptionID primitive.ObjectID, payment *billing.Payment) error {
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	// If subscription is in trial and this is the first payment, activate it
	if subscription.Status == billing.SubscriptionStatusTrial {
		subscription.Status = billing.SubscriptionStatusActive
		return s.repo.Subscriptions().Update(ctx, subscription)
	}

	// If subscription was suspended due to failed payment, reactivate it
	if subscription.Status == billing.SubscriptionStatusSuspended {
		subscription.Reactivate()
		return s.repo.Subscriptions().Update(ctx, subscription)
	}

	return nil
}

// handleFailedPayment processes a failed payment for a subscription
func (s *service) handleFailedPayment(ctx context.Context, subscriptionID primitive.ObjectID, payment *billing.Payment) error {
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	// If this is a trial payment failure, cancel the subscription
	if subscription.Status == billing.SubscriptionStatusTrial {
		subscription.Cancel("Trial payment failed")
		return s.repo.Subscriptions().Update(ctx, subscription)
	}

	// For active subscriptions, suspend after payment failure
	if subscription.Status == billing.SubscriptionStatusActive {
		subscription.Suspend()
		return s.repo.Subscriptions().Update(ctx, subscription)
	}

	return nil
}

// handleRefundedPayment processes a refunded payment for a subscription
func (s *service) handleRefundedPayment(ctx context.Context, subscriptionID primitive.ObjectID, payment *billing.Payment) error {
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	// For refunded payments, always cancel with immediate access revocation
	// This is typically a provider-initiated refund (e.g., chargeback, dispute)
	subscription.Cancel("Payment refunded by provider - access revoked immediately")
	return s.repo.Subscriptions().Update(ctx, subscription)
}
