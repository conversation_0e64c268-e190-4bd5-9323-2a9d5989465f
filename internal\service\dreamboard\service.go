package dreamboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/dsoplabs/dinbora-backend/internal/service/gamification"

	_dreamboard "github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialsheet"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Service interface {
	// CRUD
	Create(ctx context.Context, board *dreamboard.Dreamboard) (string, error)
	Find(ctx context.Context, id string) (*dreamboard.Dreamboard, error)
	FindAll(ctx context.Context) ([]*dreamboard.Dreamboard, error)
	FindByUser(ctx context.Context, userID string) (*dreamboard.Dreamboard, error)
	Update(ctx context.Context, board *dreamboard.Dreamboard) error
	Delete(ctx context.Context, id string) error

	// Category CRUD
	CreateCategory(ctx context.Context, board *dreamboard.Dreamboard, category *dreamboard.Category) (*dreamboard.Category, error)
	FindCategory(ctx context.Context, board *dreamboard.Dreamboard, categoryID string) (*dreamboard.Category, error)
	UpdateCategory(ctx context.Context, board *dreamboard.Dreamboard, category *dreamboard.Category) (*dreamboard.Category, error)
	DeleteCategory(ctx context.Context, board *dreamboard.Dreamboard, categoryID string) error

	// Dream CRUD
	CreateDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) (*CreateDreamResponseDTO, error)
	FindDream(ctx context.Context, board *dreamboard.Dreamboard, dreamID string) (*dreamboard.Dream, error)
	FindPersonalDreams(ctx context.Context, userID string) ([]*dreamboard.Dream, error)
	FindSharedDreams(ctx context.Context, userID string) ([]*dreamboard.Dream, error)
	FindDreamDetails(ctx context.Context, dreamID string, userID string) (*DreamDetails, error)
	UpdateDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) ([]*dreamboard.Dream, error)
	RemoveDream(ctx context.Context, board *dreamboard.Dreamboard, dream *dreamboard.Dream) ([]*dreamboard.Dream, error)

	// Dream Management
	CalculateDreamDuration(ctx context.Context, dreamID string, estimatedCost monetary.Amount) (*int, error)

	// Invitation Management
	InviteDetails(ctx context.Context, code string) (*InviteDetailsDTO, error)
	JoinSharedDream(ctx context.Context, token string, userID string, isCreator bool, monthlyPledgedAmount monetary.Amount) (*dreamboard.Contribution, error)

	// Share Link Management
	CreateShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error)
	FindShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error)
	FindShareLinkByCode(ctx context.Context, code string) (*dreamboard.ShareLink, error)
	UpdateShareLinkStatus(ctx context.Context, dreamID string, isEnabled bool) error
	RegenerateShareLink(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error)

	// Contribution Management
	CreateContribution(ctx context.Context, contribution *dreamboard.Contribution) (*dreamboard.Contribution, error)
	FindContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error)
	FindActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error)
	FindContributionsByUserID(ctx context.Context, userID string) ([]*dreamboard.Contribution, error)
	FindContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*dreamboard.Contribution, error)
	UpdateContribution(ctx context.Context, contribution *dreamboard.Contribution) error
	UpdateContributionStatus(ctx context.Context, dreamID string, status dreamboard.ContributionStatus) error
	DeleteContribution(ctx context.Context, contributionID string) error

	// Utility
	Initialize(ctx context.Context, userID string) error
}

type service struct {
	Repository               _dreamboard.Repository
	FinancialSheetRepository financialsheet.Repository
	GamificationService      gamification.Service
}

func New(repository _dreamboard.Repository, financialSheetRepository financialsheet.Repository, gamificationService gamification.Service) Service {
	return &service{
		Repository:               repository,
		FinancialSheetRepository: financialSheetRepository,
		GamificationService:      gamificationService,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, board *dreamboard.Dreamboard) (string, error) {
	currentTime := time.Now()
	board.CreatedAt = currentTime
	board.UpdatedAt = currentTime
	board.ComputeTotals() // Initialize the computed fields

	dreamboardID, err := s.Repository.Create(ctx, board)
	if err != nil {
		return "", err
	}

	return dreamboardID, err
}

func (s *service) Find(ctx context.Context, id string) (*dreamboard.Dreamboard, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Service, "invalid dreamboard id", errors.BadRequest, err)
	}

	board, err := s.Repository.Find(ctx, objID)
	if err != nil {
		return nil, err
	}

	// Get SavedAmount from Financial Sheet Balance
	financialsheet, err := s.FinancialSheetRepository.FindByUser(ctx, board.User)
	if err != nil {
		return nil, err
	}

	if board != nil && !board.ObjectID.IsZero() {
		board.ID = board.ObjectID.Hex()
	}

	for _, dream := range board.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Update computed fields before returning
	board.CalculateSavedAmount(financialsheet)
	board.ComputeTotals()

	return board, nil
}

func (s *service) FindAll(ctx context.Context) ([]*dreamboard.Dreamboard, error) {
	boards, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	for _, board := range boards {
		// Get SavedAmount from Financial Sheet Balance
		financialsheet, err := s.FinancialSheetRepository.FindByUser(ctx, board.User)
		if err != nil {
			return nil, err
		}

		if board != nil && !board.ObjectID.IsZero() {
			board.ID = board.ObjectID.Hex()
		}

		for _, dream := range board.Dreams {
			if dream != nil && !dream.ObjectID.IsZero() {
				dream.ID = dream.ObjectID.Hex()
			}
		}

		// Update computed fields before returning
		board.CalculateSavedAmount(financialsheet)
		board.ComputeTotals()
	}

	return boards, nil
}

func (s *service) FindByUser(ctx context.Context, userID string) (*dreamboard.Dreamboard, error) {
	board, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		if err.(*errors.DomainError).Kind() != errors.NotFound {
			return nil, err
		}
		// Initialize new dreamboard for user
		if err := s.Initialize(ctx, userID); err != nil {
			return nil, err
		}

		// Fetch the newly created dreamboard
		board, err = s.Repository.FindByUser(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	// Get SavedAmount from Financial Sheet Balance
	financialsheet, err := s.FinancialSheetRepository.FindByUser(ctx, board.User)
	if err != nil {
		return nil, err
	}

	if board != nil && !board.ObjectID.IsZero() {
		board.ID = board.ObjectID.Hex()

	}

	for _, dream := range board.Dreams {
		if dream != nil && !dream.ObjectID.IsZero() {
			dream.ID = dream.ObjectID.Hex()
		}
	}

	// Update computed fields before returning
	board.CalculateSavedAmount(financialsheet)
	board.ComputeTotals()

	return board, nil
}

func (s *service) Update(ctx context.Context, board *dreamboard.Dreamboard) error {
	if err := board.Validate(); err != nil {
		return err
	}

	if board.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(board.ID)
		if err != nil {
			return errors.New(errors.Service, "invalid dreamboard ID", errors.Validation, err)
		}
		board.ObjectID = objID
	}

	board.UpdatedAt = time.Now()
	board.ComputeTotals() // Update computed fields before saving

	err := s.Repository.Update(ctx, board)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) Delete(ctx context.Context, id string) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid dreamboard ID", errors.BadRequest, err)
	}

	return s.Repository.Delete(ctx, objID)
}

// Utility
func (s *service) Initialize(ctx context.Context, userID string) error {
	existing, err := s.Repository.FindByUser(ctx, userID)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return err
	}

	if existing != nil {
		return errors.New(errors.Service, "dreamboard already exists", errors.Conflict, nil)
	}

	currentTime := time.Now()
	newBoard := &dreamboard.Dreamboard{
		User:       userID,
		Dreams:     []*dreamboard.Dream{},
		Categories: []*dreamboard.Category{}, // Add default categories after creation
		CreatedAt:  currentTime,
		UpdatedAt:  currentTime,
	}

	newBoard.ComputeTotals() // Initialize computed fields to zero since there are no dreams

	if err := newBoard.Validate(); err != nil {
		return err
	}

	boardID, err := s.Repository.Create(ctx, newBoard)
	if err != nil {
		return err
	}

	objID, err := primitive.ObjectIDFromHex(boardID)
	if err != nil {
		return errors.New(errors.Service, "invalid board ID", errors.Internal, err)
	}

	// Add default categories
	categories := []dreamboard.Category{
		dreamboard.Professional,
		dreamboard.Financial,
		dreamboard.Leisure,
		dreamboard.Emotional,
		dreamboard.Intellectual,
		dreamboard.Spiritual,
		dreamboard.Physical,
		dreamboard.Intimate,
		dreamboard.Social,
		dreamboard.Familial,
	}

	return s.Repository.CreateCategories(ctx, objID, categories)
}
