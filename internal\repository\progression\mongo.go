package progression

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.PROGRESSIONS_COLLECTION),
	}

	// Create unique index on user field
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "user", Value: 1}},
			Options: options.Index().SetUnique(true).SetName("user"),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on progress.user field")
		if err := db.Client().Disconnect(context.Background()); err != nil {
			log.Println("warning: failed to disconnect from MongoDB client")
		}
	}

	return repo
}

// Progression CRUD
func (m mongoDB) Create(ctx context.Context, progression *progression.Progression) error {
	_, err := m.collection.InsertOne(ctx, progression)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "progress already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create progress", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) Find(ctx context.Context, id string) (*progression.Progression, error) {
	objectId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid progress ID format", errors.Validation, err)
	}

	var progression progression.Progression
	if err = m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectId}}).Decode(&progression); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "progress not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find progress", errors.Internal, err)
	}

	return &progression, nil
}

func (m mongoDB) FindByUser(ctx context.Context, userId string) (*progression.Progression, error) {
	var progression progression.Progression
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "user", Value: userId}}).Decode(&progression); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "progress not found for user", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find progress by user", errors.Internal, err)
	}

	return &progression, nil
}

func (m mongoDB) Update(ctx context.Context, progression *progression.Progression) error {
	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: progression.ObjectID}},
		primitive.M{"$set": progression})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "progress update would cause conflict", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update progress", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "progress not found for update", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid progress ID format", errors.Validation, err)
	}

	result, err := m.collection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectID}})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete progress", errors.Internal, err)
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "progress not found for deletion", errors.NotFound, nil)
	}

	return nil
}

// FindForCards retrieves only the trail progression data needed for cards
func (m mongoDB) FindForCards(ctx context.Context, userId string) (map[string]*progression.Trail, error) {
	// Project only the fields needed for trail cards
	projection := bson.D{
		{Key: "trails.id", Value: 1},
		{Key: "trails.total", Value: 1},
		{Key: "trails.available", Value: 1},
		{Key: "trails.current", Value: 1},
	}
	opts := options.FindOne().SetProjection(projection)

	filter := bson.D{{Key: "user", Value: userId}}

	var result struct {
		Trails []*progression.Trail `bson:"trails"`
	}

	if err := m.collection.FindOne(ctx, filter, opts).Decode(&result); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "progress not found for user", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find trail progressions for cards", errors.Internal, err)
	}

	// Convert the array to a map for easier lookup
	trailMap := make(map[string]*progression.Trail)
	for _, trail := range result.Trails {
		if trail.ID != "" {
			trailMap[trail.ID] = trail
		}
	}

	return trailMap, nil
}
