package billing

import (
	"strings"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/imdario/mergo"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Plan defines a subscription tier available for purchase
type Plan struct {
	ObjectID primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID       string             `json:"id,omitempty" bson:"-"`

	Name        string          `json:"name" bson:"name" validate:"required,min=3,max=100"`
	Description string          `json:"description" bson:"description" validate:"max=500"`
	Price       monetary.Amount `json:"price" bson:"price" validate:"required,min=0"`
	Currency    string          `json:"currency" bson:"currency" validate:"required,len=3"`

	// Features for access control
	Features []string `json:"features" bson:"features"`

	// External provider IDs
	HotmartProductID string `json:"hotmartProductId,omitempty" bson:"hotmartProductId,omitempty"`
	AppleProductID   string `json:"appleProductId,omitempty" bson:"appleProductId,omitempty"`

	// Plan configuration
	DurationMonths int  `json:"durationMonths" bson:"durationMonths" validate:"required,min=1,max=120"` // Default: 12 months
	TrialDays      int  `json:"trialDays" bson:"trialDays" validate:"min=0,max=30"`                     // Default: 7 days
	AutoRenew      bool `json:"autoRenew" bson:"autoRenew"`                                             // Default: true

	// Status
	Status PlanStatus `json:"status" bson:"status" validate:"required"`

	// Metadata
	CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" bson:"updatedAt"`
}

// PlanStatus represents the status of a plan
type PlanStatus string

const (
	PlanStatusActive   PlanStatus = "active"
	PlanStatusInactive PlanStatus = "inactive"
	PlanStatusArchived PlanStatus = "archived"
)

// IsValid validates the plan status
func (ps PlanStatus) IsValid() bool {
	switch ps {
	case PlanStatusActive, PlanStatusInactive, PlanStatusArchived:
		return true
	default:
		return false
	}
}

// Validate validates all fields in the plan
func (p *Plan) Validate() error {
	if len(p.Name) == 0 || len(p.Name) > 100 {
		return errors.New(errors.Model, "plan name must be between 1-100 characters", errors.Validation, nil)
	}

	if len(p.Description) > 500 {
		return errors.New(errors.Model, "plan description cannot exceed 500 characters", errors.Validation, nil)
	}

	if p.Price < 0 {
		return errors.New(errors.Model, "plan price cannot be negative", errors.Validation, nil)
	}

	if len(p.Currency) != 3 {
		return errors.New(errors.Model, "currency must be a 3-character ISO code", errors.Validation, nil)
	}

	if p.DurationMonths < 1 || p.DurationMonths > 120 {
		return errors.New(errors.Model, "plan duration must be between 1-120 months", errors.Validation, nil)
	}

	if p.TrialDays < 0 || p.TrialDays > 30 {
		return errors.New(errors.Model, "trial days must be between 0-30", errors.Validation, nil)
	}

	if !p.Status.IsValid() {
		return errors.New(errors.Model, "invalid plan status", errors.Validation, nil)
	}

	return nil
}

// SetDefaults sets default values for the plan
func (p *Plan) SetDefaults() {
	if p.DurationMonths == 0 {
		p.DurationMonths = 12 // Default: 12 months
	}

	if p.TrialDays == 0 {
		p.TrialDays = 7 // Default: 7 days trial
	}

	if p.Currency == "" {
		p.Currency = "BRL" // Default currency for Brazilian market
	}

	p.AutoRenew = true // Default: auto-renew enabled

	if p.Status == "" {
		p.Status = PlanStatusActive
	}

	now := time.Now()
	if p.CreatedAt.IsZero() {
		p.CreatedAt = now
	}
	p.UpdatedAt = now

	if p.Features == nil {
		p.Features = []string{}
	}
}

// HasFeature checks if the plan includes a specific feature
func (p *Plan) HasFeature(feature string) bool {
	for _, f := range p.Features {
		if f == feature {
			return true
		}
	}
	return false
}

// IsActive checks if the plan is currently active
func (p *Plan) IsActive() bool {
	return p.Status == PlanStatusActive
}

func (p *Plan) PrepareUpdate(newPlan *Plan) error {
	p.UpdatedAt = time.Now()
	if err := mergo.Merge(p, newPlan, mergo.WithOverride); err != nil {
		return errors.New(errors.Model, errors.UserMergeFailed, errors.Internal, err)
	}

	if err := p.validateUpdate(); err != nil {
		return err
	}

	return nil
}

func (p *Plan) validateUpdate() error {

	if strings.TrimSpace(p.Name) == "" {
		return errors.New(errors.Model, "name is required", errors.Validation, nil)
	}
	if strings.TrimSpace(p.Description) == "" {
		return errors.New(errors.Model, "description is required", errors.Validation, nil)
	}
	if p.Price <= 0 {
		return errors.New(errors.Model, "price must be greater than zero", errors.Validation, nil)
	}
	if strings.TrimSpace(p.Currency) == "" {
		return errors.New(errors.Model, "currency is required", errors.Validation, nil)
	}
	if strings.TrimSpace(p.HotmartProductID) == "" && strings.TrimSpace(p.AppleProductID) != "" {
		return errors.New(errors.Model, "hotmartProductId is required", errors.Validation, nil)
	}
	if strings.TrimSpace(p.AppleProductID) == "" && strings.TrimSpace(p.HotmartProductID) != "" {
		return errors.New(errors.Model, "appleProductId is required", errors.Validation, nil)
	}
	if p.DurationMonths <= 0 {
		return errors.New(errors.Model, "durationMonths must be greater than zero", errors.Validation, nil)
	}
	if p.TrialDays <= 0 {
		return errors.New(errors.Model, "trial duration must be greater than zero", errors.Validation, nil)
	}

	return nil
}

func (p *Plan) validateCreate() error {

	if strings.TrimSpace(p.Name) == "" {
		return errors.New(errors.Model, "name is required", errors.Validation, nil)
	}
	if strings.TrimSpace(p.Description) == "" {
		return errors.New(errors.Model, "description is required", errors.Validation, nil)
	}
	if p.Price <= 0 {
		return errors.New(errors.Model, "price must be greater than zero", errors.Validation, nil)
	}
	if strings.TrimSpace(p.Currency) == "" {
		return errors.New(errors.Model, "currency is required", errors.Validation, nil)
	}
	if strings.TrimSpace(p.HotmartProductID) == "" && strings.TrimSpace(p.AppleProductID) != "" {
		return errors.New(errors.Model, "hotmartProductId is required", errors.Validation, nil)
	}
	if strings.TrimSpace(p.AppleProductID) == "" && strings.TrimSpace(p.HotmartProductID) != "" {
		return errors.New(errors.Model, "appleProductId is required", errors.Validation, nil)
	}
	if p.DurationMonths <= 0 {
		return errors.New(errors.Model, "durationMonths must be greater than zero", errors.Validation, nil)
	}
	if p.TrialDays <= 0 {
		return errors.New(errors.Model, "trial duration must be greater than zero", errors.Validation, nil)
	}

	return nil
}

func (p *Plan) PrepareCreate() error {
	p.Name = strings.TrimSpace(p.Name)

	p.CreatedAt = time.Now()
	p.UpdatedAt = p.CreatedAt

	if err := p.validateCreate(); err != nil {
		return err
	}
	return nil
}
