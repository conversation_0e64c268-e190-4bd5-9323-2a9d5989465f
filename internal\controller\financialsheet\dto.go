package financialsheet

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

type DreamTransactionRequest struct {
	DreamID       string                       `json:"dreamId" validate:"required"`
	DreamName     string                       `json:"dreamName" validate:"required"`
	Value         monetary.Amount              `json:"value" validate:"required,min=1"`
	Date          time.Time                    `json:"date" validate:"required"`
	PaymentMethod financialsheet.PaymentMethod `json:"paymentMethod" validate:"required"`
}

type RecurringTransactionRequest struct {
	Category          financialsheet.CategoryIdentifier `json:"category" validate:"required"`
	MoneySource       financialsheet.MoneySource        `json:"moneySource" validate:"required"`
	Value             monetary.Amount                   `json:"value" validate:"required,min=1"`
	Date              time.Time                         `json:"date" validate:"required"`
	PaymentMethod     financialsheet.PaymentMethod      `json:"paymentMethod" validate:"required"`
	Type              financialsheet.CategoryType       `json:"type" validate:"required"`
	AttachedDreamID   string                            `json:"attachedDreamID,omitempty"`
	AttachedDreamName string                            `json:"attachedDreamName,omitempty"`
	RecurrenceMonths  []int                             `json:"recurrenceMonths" validate:"required,min=1,dive,min=1,max=12"`
}

type NoTransactionsResponse struct {
	Message       string `json:"message"`
	Success       bool   `json:"success"`
	CurrentStreak int    `json:"currentStreak,omitempty"`
	BestStreak    int    `json:"bestStreak,omitempty"`
}
