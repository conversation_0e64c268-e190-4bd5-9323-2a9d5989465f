package gamification

import "fmt"

// Hero represents a frontend gamification card what to display to the user
type Hero struct {
	Key   string `json:"key"`
	Title string `json:"title"`
	Image string `json:"image"`
	Order int    `json:"order"` // Lower number = higher priority
}

const (
	HeroDinboraPlus       = "user-dashboard"
	HeroFinancialTree     = "financial-tree"
	HeroFinancialNote     = "financial-note"
	HeroDreamBoard        = "dream-board"
	HeroFinancialPlanning = "financial-planning"
)

var Heroes = map[string]Hero{
	HeroDinboraPlus: {
		Key:   HeroDinboraPlus,
		Title: "Dinbora+",
		Image: "https://images.dinbora.com.br/heroes/dinbora-plus.png",
		Order: 1,
	},
	HeroFinancialTree: {
		Key:   HeroFinancialTree,
		Title: "Meu DNA",
		Image: "https://images.dinbora.com.br/heroes/financial-tree.png",
		Order: 2,
	},
	HeroFinancialNote: {
		Key:   HeroFinancialNote,
		Title: "Apontamento",
		Image: "https://images.dinbora.com.br/heroes/financial-note.png",
		Order: 3,
	},
	HeroDreamBoard: {
		Key:   HeroDreamBoard,
		Title: "Sonhos",
		Image: "https://images.dinbora.com.br/heroes/dreamboard.png",
		Order: 4,
	},
	HeroFinancialPlanning: {
		Key:   HeroFinancialPlanning,
		Title: "Planejamento",
		Image: "https://images.dinbora.com.br/heroes/financial-planning.png",
		Order: 5,
	},
}

func GetHero(key string) *Hero {
	if hero, ok := Heroes[key]; ok {
		return &hero
	}
	return nil
}

func GetAllHeroes() []*Hero {
	heroes := make([]*Hero, 0, len(Heroes))
	for _, hero := range Heroes {
		heroes = append(heroes, &hero)
	}
	return heroes
}

func MustGetHero(key string) *Hero {
	hero, ok := Heroes[key]
	if !ok {
		panic(fmt.Sprintf("gamification: hero with key '%s' does not exist", key))
	}
	return &hero
}

// const hero = [
//     { key: "user-dashboard", title: "learn.hero.dinboraplus", image: require("@assets/images/dinbora-plus.png") },
//     { key: "financial-tree", title: "learn.hero.financialTree", image: require("@assets/images/financial-tree.png") },
//     { key: "dream-board", title: "learn.hero.dreamBoard", image: require("@assets/images/dreamboard.png") },
//     { key: "financial-note", title: "learn.hero.financialNote", image: require("@assets/images/financial-note.png") },
//     { key: "financial-planning", title: "learn.hero.financialPlanning", image: require("@assets/images/financial-planning.png") }
//   ];
