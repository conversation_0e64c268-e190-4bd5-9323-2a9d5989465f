package sendgrid

import (
	"encoding/base64"
	"net/url"
	"os"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/sendgrid/rest"
	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
)

// DEPRECATED: This SendGrid service is deprecated and will be removed.
// Use the new notification service at internal/service/notification instead.
type Service interface {
	// Core
	SendEmail(*model.User, string) (*rest.Response, error)
	SendSubscribeEmail(*model.User, string) (*rest.Response, error)
	SendSubscribeEmailNewUser(*model.User, string) (*rest.Response, error)
	SendCancelEmail(*model.User, string) (*rest.Response, error)

	// Utility
	ForgotPassword(*model.User, string) (*rest.Response, error)
}

type service struct {
	Client  *sendgrid.Client
	From    *mail.Email
	To      *mail.Email
	Subject string
}

type SendGridResponse struct {
	Message string `json:"message"`
}

const (
	ArkcBeginner = "62a599a39057732b767ab5ac"
	ArkcPremium  = "62a59b629057732b767ab5ad"
	ArkcBlack    = "62a59c5c9057732b767ab5ae"
	ArkcCripto   = "62a599a39057732b767ab5af"

	Company      = "Suporte Dinbora"
	CompanyEmail = "<EMAIL>"

	ForgotPasswordTemplateID = "d-8a39f64716374fd99585bb679b8eef85"
)

func New(sendgridClient *sendgrid.Client) Service {
	return &service{
		Client: sendgridClient,
		From:   mail.NewEmail(Company, CompanyEmail),
	}
}

// Core operations
func (s *service) SendEmail(user *model.User, templateID string) (*rest.Response, error) {
	// Create a new email
	message := mail.NewV3Mail()
	message.SetFrom(s.From)
	message.SetTemplateID(templateID)

	s.To = &mail.Email{
		Address: user.Email,
		Name:    user.Name,
	}

	personalization := mail.NewPersonalization()
	to := []*mail.Email{s.To}

	personalization.AddTos(to...)
	message.AddPersonalizations(personalization)

	// Add ASM Information
	asm := &mail.Asm{
		GroupID:         48291,
		GroupsToDisplay: []int{48291},
	}
	message.SetASM(asm)

	response, err := s.Client.Send(message)

	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s *service) SendSubscribeEmail(user *model.User, planID string) (*rest.Response, error) {

	var planTemplateID string

	switch planID {
	case ArkcBeginner:
		planTemplateID = "d-4f22ad14a7bb4a13b33cb5c70f58d54e"
	case ArkcPremium:
		planTemplateID = "d-88f654250f714ceab3550a8a207b4d5e"
	case ArkcBlack:
		planTemplateID = "d-857ff38efde541ac92ff306ed5309447"
	case ArkcCripto:
		planTemplateID = "d-a7f652d4cab44391beed82ca9a038acf" // change after create the template in send grid
	}

	response, err := s.SendEmail(user, planTemplateID)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s *service) SendSubscribeEmailNewUser(user *model.User, planID string) (*rest.Response, error) {

	var planTemplateID string

	switch planID {
	case ArkcBeginner:
		planTemplateID = "d-27add60e71914907a9467fcb781c3f09"
	case ArkcPremium:
		planTemplateID = "d-7c2fcd5439f64ade86faa065fea77a62"
	case ArkcBlack:
		planTemplateID = "d-77393bd4e609457a823586f6c6975978"
	case ArkcCripto:
		planTemplateID = "d-d9130675c29041a58333ba57762d5833"
	}

	userToken, err := token.Standalone(user)
	if err != nil {
		return nil, err
	}

	resetLink := os.Getenv("APP_URL") + "/nova-senha/" + url.QueryEscape(base64.StdEncoding.WithPadding(base64.NoPadding).EncodeToString([]byte(userToken)))

	response, err := s.SetPasswordNewUser(user, resetLink, planTemplateID)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s *service) SendCancelEmail(user *model.User, planID string) (*rest.Response, error) {

	var planTemplateID string

	switch planID {
	case ArkcBeginner:
		planTemplateID = "d-a6b278f38e924b698741791fb71efdbe"
	case ArkcPremium:
		planTemplateID = "d-5d5032252f3246658858b2af40393513"
	case ArkcBlack:
		planTemplateID = "d-06d6acacc1744d3f86d8bfe3f8ea44fa"
	case ArkcCripto:
		planTemplateID = "d-e0bd0c9697b94ba790643de8f42f2c9c"
	}

	response, err := s.SendEmail(user, planTemplateID)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// Utility
func (s *service) ForgotPassword(user *model.User, resetLink string) (*rest.Response, error) {
	// Create a new email
	message := mail.NewV3Mail()
	message.SetFrom(s.From)
	message.SetTemplateID(ForgotPasswordTemplateID)

	s.To = &mail.Email{
		Address: user.Email,
		Name:    user.Name,
	}

	personalization := mail.NewPersonalization()
	to := []*mail.Email{s.To}

	personalization.AddTos(to...)
	message.AddPersonalizations(personalization)
	message.Personalizations[0].SetDynamicTemplateData("resetLink", resetLink)

	// Add ASM Information
	asm := &mail.Asm{
		GroupID:         48291,
		GroupsToDisplay: []int{48291},
	}
	message.SetASM(asm)

	response, err := s.Client.Send(message)

	if err != nil {
		return nil, err
	}

	return response, nil
}

func (s *service) SetPasswordNewUser(user *model.User, resetLink string, templateID string) (*rest.Response, error) {
	// Create a new email
	message := mail.NewV3Mail()
	message.SetFrom(s.From)
	message.SetTemplateID(templateID)

	s.To = &mail.Email{
		Address: user.Email,
		Name:    user.Name,
	}

	personalization := mail.NewPersonalization()
	to := []*mail.Email{s.To}

	personalization.AddTos(to...)
	message.AddPersonalizations(personalization)
	message.Personalizations[0].SetDynamicTemplateData("resetLink", resetLink)

	// Add ASM Information
	asm := &mail.Asm{
		GroupID:         48291,
		GroupsToDisplay: []int{48291},
	}
	message.SetASM(asm)

	response, err := s.Client.Send(message)

	if err != nil {
		return nil, err
	}

	return response, nil
}
