package financialdna

import (
	"context"
	"log"
	"mime/multipart"
	"os"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialdna"
	_financialdna "github.com/dsoplabs/dinbora-backend/internal/repository/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/service/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/service/s3"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Service interface {
	// CRUD
	Create(ctx context.Context, userID string) (*financialdna.FinancialDNATree, error)
	Find(ctx context.Context, id string) (*financialdna.FinancialDNATree, error)
	FindByUser(ctx context.Context, userID string) (*financialdna.FinancialDNATree, error)
	Update(ctx context.Context, tree *financialdna.FinancialDNATree) error
	Delete(ctx context.Context, id string) error

	// Member CRUD
	PatchMember(ctx context.Context, userID, memberID string, member *financialdna.FamilyMember, photo *multipart.FileHeader) (*financialdna.FinancialDNATree, error)
	DeleteMember(ctx context.Context, userID, memberID string) (*financialdna.FinancialDNATree, error)

	CreateChild(ctx context.Context, userID, parentID string, child *financialdna.FamilyMember) (*financialdna.FinancialDNATree, error)
	DeleteChild(ctx context.Context, userID, parentID, childID string) (*financialdna.FinancialDNATree, error)

	// Utility
	Initialize(ctx context.Context, userID string) error
}

type service struct {
	repository          _financialdna.Repository
	S3Service           s3.Service
	GamificationService gamification.Service
}

func New(repository _financialdna.Repository, s3Service s3.Service, gamificationService gamification.Service) Service {
	return &service{
		repository:          repository,
		S3Service:           s3Service,
		GamificationService: gamificationService,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, userID string) (*financialdna.FinancialDNATree, error) {
	tree := financialdna.New(userID)
	_, err := s.repository.Create(ctx, tree)
	if err != nil {
		return nil, err
	}

	return tree, nil
}

func (s *service) Find(ctx context.Context, id string) (*financialdna.FinancialDNATree, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Service, errors.FinancialDNAInvalidID, errors.BadRequest, err)
	}

	financialDNA, err := s.repository.Find(ctx, objID)
	if err != nil {
		return nil, err
	}

	if financialDNA != nil && !financialDNA.ObjectID.IsZero() {
		financialDNA.ID = financialDNA.ObjectID.Hex()
	}

	return financialDNA, nil
}

func (s *service) FindByUser(ctx context.Context, userID string) (*financialdna.FinancialDNATree, error) {
	userObjID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return nil, errors.New(errors.Service, errors.UserInvalidID, errors.BadRequest, err)
	}

	financialDNA, err := s.repository.FindByUser(ctx, userObjID)
	if err != nil {
		return nil, err
	}

	if financialDNA != nil && !financialDNA.ObjectID.IsZero() {
		financialDNA.ID = financialDNA.ObjectID.Hex()
	}

	for _, member := range financialDNA.Members {
		if member != nil && !member.ObjectID.IsZero() {
			member.ID = member.ObjectID.Hex()
		}
	}

	return financialDNA, nil
}

func (s *service) Update(ctx context.Context, tree *financialdna.FinancialDNATree) error {
	return s.repository.Update(ctx, tree)
}

func (s *service) Delete(ctx context.Context, id string) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, errors.FinancialDNAInvalidID, errors.BadRequest, err)
	}

	return s.repository.Delete(ctx, objID)
}

// Member CRUD
func (s *service) PatchMember(ctx context.Context, userID, memberID string, member *financialdna.FamilyMember, photo *multipart.FileHeader) (*financialdna.FinancialDNATree, error) {
	tree, err := s.FindByUser(ctx, userID)
	if err != nil {
		return nil, err // Error finding the tree for the user
	}

	mergedTree, err := s.mergeMembers(ctx, tree, memberID, member, photo)
	if err != nil {
		return nil, err
	}

	// Recalculate metrics before saving
	mergedTree.CalculateTreeProgress()
	mergedTree.CalculateFinancialDistribution()
	mergedTree.CalculateBreakCycles()

	err = s.Update(ctx, mergedTree)
	if err != nil {
		return nil, err // Error updating the tree in the repository
	}

	// Check for achievements after successful family member update
	if s.GamificationService != nil {
		if err := s.GamificationService.CheckDNAAchievement(ctx, userID); err != nil {
			// Log error but don't fail the member update
			log.Printf("Failed to check achievements for user %s: %v", userID, err)
		}
	}

	// Return the potentially updated tree
	return mergedTree, nil
}

func (s *service) CreateChild(ctx context.Context, userID, parentID string, child *financialdna.FamilyMember) (*financialdna.FinancialDNATree, error) {
	tree, err := s.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Find the parent member by ID
	parent := tree.FindMember(parentID)
	if parent == nil {
		return nil, errors.New(errors.Service, "parent not found", errors.NotFound, nil)
	}

	// Check if the parent is the central user ("me") by checking their icon
	if parent.Icon == financialdna.FamilyMemberIconMe {
		child.Icon = financialdna.FamilyMemberIconChild
	} else {
		child.Icon = financialdna.FamilyMemberIconGrandChild
	}

	child.FinancialStatus = financialdna.FinancialStatusUndefined
	_, err = tree.AddChild(parent, child.Name, child.Icon, child.FinancialStatus)
	if err != nil {
		return nil, err
	}

	// Recalculate metrics before saving
	tree.CalculateTreeProgress()
	tree.CalculateFinancialDistribution()
	tree.CalculateBreakCycles()

	if err := s.Update(ctx, tree); err != nil {
		return nil, err
	}

	return tree, nil
}

func (s *service) DeleteMember(ctx context.Context, userID, memberID string) (*financialdna.FinancialDNATree, error) {
	tree, err := s.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Remove the member from the tree
	err = tree.RemoveMember(memberID)
	if err != nil {
		return nil, err
	}

	// Recalculate metrics before saving
	tree.CalculateTreeProgress()
	tree.CalculateFinancialDistribution()
	tree.CalculateBreakCycles()

	if err := s.Update(ctx, tree); err != nil {
		return nil, err
	}

	return tree, nil
}

func (s *service) DeleteChild(ctx context.Context, userID, parentID, childID string) (*financialdna.FinancialDNATree, error) {
	tree, err := s.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Find the parent member by ID
	parent := tree.FindMember(parentID)
	if parent == nil {
		return nil, errors.New(errors.Service, "parent not found", errors.NotFound, nil)
	}

	// Remove the child from the parent's ChildrenIDs
	err = tree.RemoveChild(parent, childID)
	if err != nil {
		return nil, err
	}

	// Recalculate metrics before saving
	tree.CalculateTreeProgress()
	tree.CalculateFinancialDistribution()
	tree.CalculateBreakCycles()

	if err := s.Update(ctx, tree); err != nil {
		return nil, err
	}

	return tree, nil
}

// Utility
func (s *service) Initialize(ctx context.Context, userID string) error {
	tree := financialdna.New(userID)

	// Populate the tree with default family members
	err := s.populateFinancialDNATree(tree)
	if err != nil {
		return err
	}

	// Recalculate metrics before creating
	tree.CalculateTreeProgress()
	tree.CalculateFinancialDistribution()
	tree.CalculateBreakCycles()

	_, err = s.repository.Create(ctx, tree)
	if err != nil {
		return err
	}

	return nil
}

// Helper
func (s *service) populateFinancialDNATree(tree *financialdna.FinancialDNATree) error {
	defaultFinancialStatus := financialdna.FinancialStatusUndefined

	// Add yourself ("Eu") as the central node
	me, err := tree.AddMember("Eu", financialdna.FamilyMemberIconMe, defaultFinancialStatus)
	if err != nil {
		return errors.New(errors.Service, "error adding me", errors.Internal, err)
	}

	// Add parents ("Pai" and "Mãe")
	father, mother, err := tree.AddParents(me, "Pai", "Mãe", financialdna.FamilyMemberIconFather, financialdna.FamilyMemberIconMother, defaultFinancialStatus)
	if err != nil {
		return err
	}

	// Add paternal grandparents ("Avô Paterno" and "Avó Paterna")
	paternalGrandfather, paternalGrandmother, err := tree.AddParents(father, "Avô Paterno", "Avó Paterna", financialdna.FamilyMemberIconGrandfather, financialdna.FamilyMemberIconGrandmother, defaultFinancialStatus)
	if err != nil {
		return err
	}

	// Add maternal grandparents ("Avô Materno" and "Avó Materna")
	maternalGrandfather, maternalGrandmother, err := tree.AddParents(mother, "Avô Materno", "Avó Materna", financialdna.FamilyMemberIconGrandfather, financialdna.FamilyMemberIconGrandmother, defaultFinancialStatus)
	if err != nil {
		return err
	}

	// Add paternal greatgrandparents ("Bisavô Paterno 1", "Bisavó Paterna 1", "Bisavô Paterno 2" and "Bisavó Paterna 2")
	_, _, err = tree.AddParents(paternalGrandfather, "Bisavô Paterno 1", "Bisavó Paterna 1", financialdna.FamilyMemberIconGreatgrandfather, financialdna.FamilyMemberIconGreatgrandmother, defaultFinancialStatus)
	if err != nil {
		return err
	}
	_, _, err = tree.AddParents(paternalGrandmother, "Bisavô Paterno 2", "Bisavó Paterna 2", financialdna.FamilyMemberIconGreatgrandfather, financialdna.FamilyMemberIconGreatgrandmother, defaultFinancialStatus)
	if err != nil {
		return err
	}

	// Add maternal greatgrandparents ("Bisavô Paterno 1", "Bisavó Paterna 1", "Bisavô Paterno 2" and "Bisavó Paterna 2")
	_, _, err = tree.AddParents(maternalGrandfather, "Bisavô Materno 1", "Bisavó Materna 1", financialdna.FamilyMemberIconGreatgrandfather, financialdna.FamilyMemberIconGreatgrandmother, defaultFinancialStatus)
	if err != nil {
		return err
	}
	_, _, err = tree.AddParents(maternalGrandmother, "Bisavô Materno 2", "Bisavó Materna 2", financialdna.FamilyMemberIconGreatgrandfather, financialdna.FamilyMemberIconGreatgrandmother, defaultFinancialStatus)
	if err != nil {
		return err
	}

	return nil
}

func (s *service) mergeMembers(ctx context.Context, tree *financialdna.FinancialDNATree, memberID string, member *financialdna.FamilyMember, photo *multipart.FileHeader) (*financialdna.FinancialDNATree, error) {
	foundMember := tree.FindMember(memberID)
	if foundMember == nil {
		return nil, errors.New(errors.Service, "member not found", errors.NotFound, nil)
	}

	// Update the member's details with the input data
	// Check if name is not empty and set it to the current name if so
	if member.Name != "" {
		foundMember.Name = member.Name
	}

	if member.FinancialStatus.IsValid() {
		foundMember.FinancialStatus = member.FinancialStatus
	}

	// Upload file to S3
	if photo != nil {
		fileURL, err := s.S3Service.UploadFile(ctx, photo, os.Getenv("AWS_S3_FINANCIALDNA_PHOTOS_FOLDER"))
		if err != nil {
			return nil, err
		}
		// Update member's icon
		defaultIconPrefix := "https://images.dinbora.com.br/dna-financeiro/"
		oldIconURL := string(foundMember.Icon)

		if oldIconURL != fileURL && !strings.HasPrefix(oldIconURL, defaultIconPrefix) {
			// Delete old icon if it exists, is different from the new one, and does not have the default prefix
			err := s.S3Service.DeleteFile(ctx, oldIconURL)
			if err != nil {
				// Log the error but don't necessarily block the update if deletion fails.
				// Depending on requirements, you might want to return the error.
				log.Printf("Error deleting old icon '%s': %v. Proceeding with icon update.", oldIconURL, err)
			}
		}

		// Update member's icon URL
		foundMember.Icon = financialdna.FamilyMemberIcon(fileURL)
	}

	return tree, nil
}
