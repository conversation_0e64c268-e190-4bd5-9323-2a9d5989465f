package gamification

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/service/gamification"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// Endpoints
	FindUserAchievements() echo.HandlerFunc
}

type controller struct {
	Service gamification.Service
}

func New(service gamification.Service) Controller {
	return &controller{
		Service: service,
	}
}

// Routes
func (c *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	gamificationGroup := currentGroup.Group("/gamification", middlewares.AuthGuard())

	// Heroes endpoint
	gamificationGroup.GET("/heroes", c.DetermineVisibleHeroesForUser())

	// User achievements endpoint
	gamificationGroup.GET("/achievements", c.FindUserAchievements())
}

// DetermineVisibleHeroesForUser returns all heroes visible to the authenticated user
func (c *controller) DetermineVisibleHeroesForUser() echo.HandlerFunc {
	return func(ctx echo.Context) error {
		// Extract user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(ctx.Request())
		if err != nil {
			return err
		}

		// Get visible heroes for the user
		heroes, err := c.Service.DetermineVisibleHeroesForUser(ctx.Request().Context(), userToken.Uid)
		if err != nil {
			return err
		}

		return ctx.JSON(http.StatusOK, heroes)
	}
}

// FindUserAchievements returns all achievements for the authenticated user
func (c *controller) FindUserAchievements() echo.HandlerFunc {
	return func(ctx echo.Context) error {
		// Extract user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(ctx.Request())
		if err != nil {
			return err
		}

		// Get user achievements
		achievements, err := c.Service.FindUserAchievements(ctx.Request().Context(), userToken.Uid)
		if err != nil {
			return err
		}

		// Get content achievements
		contentAchievements, err := c.Service.FindContentAchievements(ctx.Request().Context())
		if err != nil {
			return err
		}

		// Build the slice of achievements using the DTO
		achievementsDTO := make([]*Achievement, 0, len(achievements))
		for _, achievement := range contentAchievements {
			achievementsDTO = append(achievementsDTO, &Achievement{
				ID:         achievement.ObjectID.Hex(),
				Name:       achievement.Name,
				Identifier: achievement.Identifier,
				Level:      achievement.Level,
				Logo:       achievement.Logo,
				Conquered:  false,
			})
			for _, userAchievement := range achievements {
				if userAchievement.Identifier == achievement.Identifier {
					achievementsDTO[len(achievementsDTO)-1].Conquered = true
					break
				}
			}
		}

		// Return the achievements
		return ctx.JSON(http.StatusOK, achievementsDTO)
	}
}
