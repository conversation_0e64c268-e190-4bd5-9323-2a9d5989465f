package google

import (
	"context"
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"

	"github.com/dsoplabs/dinbora-backend/internal/service/firebase"
	"github.com/dsoplabs/dinbora-backend/internal/service/google"
	"github.com/dsoplabs/dinbora-backend/internal/service/s3"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// OAuth
	CallBackFromGoogleRegister() echo.HandlerFunc
	CallBackFromGoogleLogin() echo.HandlerFunc
}

type controller struct {
	Service         google.Service
	FirebaseService firebase.Service
}

func New(userService user.Service, s3Service s3.Service, firebaseService firebase.Service) Controller {
	return &controller{
		Service:         google.New(userService, s3Service),
		FirebaseService: firebaseService,
	}
}

// Routes
func (gc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	authGroup := currentGroup.Group("/auth")

	// authGroup.GET("oauth2/google", gc.InitLogin())
	// authGroup.GET("oauth2/callback-google", gc.CallBackFromGoogle())
	authGroup.POST("/oauth2/callback/google/register", gc.CallBackFromGoogleRegister())
	authGroup.POST("/oauth2/callback/google/login", gc.CallBackFromGoogleLogin())
}

// OAuth
// Register/Login
func (gc *controller) CallBackFromGoogleRegister() echo.HandlerFunc {
	return func(c echo.Context) error {
		referralCode := c.QueryParam("referral")

		// Get access token from form
		accessToken := c.FormValue("access")
		if accessToken == "" {
			return errors.New(errors.Controller, "access token is required", errors.Validation, nil)
		}

		// Parse onboarding data from form (following auth pattern)
		var onboarding *model.Onboarding
		ageRangeIDStr := c.FormValue("onboarding[ageRange]")
		financialSituationIDStr := c.FormValue("onboarding[financialSituation]")
		financialGoalIDStr := c.FormValue("onboarding[financialGoal]")
		firstPersonalInterestIDStr := c.FormValue("onboarding[personalInterests][0]")

		// Initialize Onboarding only if there's an indication of its data
		if ageRangeIDStr != "" || financialSituationIDStr != "" || financialGoalIDStr != "" || firstPersonalInterestIDStr != "" {
			onboarding = new(model.Onboarding)

			if idVal, ok := StringToByte(ageRangeIDStr); ok {
				onboarding.AgeRange.ID = idVal
			} else if ageRangeIDStr != "" {
				return errors.New(errors.Controller, "invalid format for onboarding[ageRange]", errors.Validation, nil)
			}

			if idVal, ok := StringToByte(financialSituationIDStr); ok {
				onboarding.FinancialSituation.ID = idVal
			} else if financialSituationIDStr != "" {
				return errors.New(errors.Controller, "invalid format for onboarding[financialSituation]", errors.Validation, nil)
			}

			if idVal, ok := StringToByte(financialGoalIDStr); ok {
				onboarding.FinancialGoal.ID = idVal
			} else if financialGoalIDStr != "" {
				return errors.New(errors.Controller, "invalid format for onboarding[financialGoal]", errors.Validation, nil)
			}

			var interests []model.PersonalInterests
			idx := 0
			for {
				interestKey := fmt.Sprintf("onboarding[personalInterests][%d]", idx)
				interestIDStr := c.FormValue(interestKey)
				if interestIDStr == "" {
					break
				}

				if idVal, ok := StringToByte(interestIDStr); ok {
					interests = append(interests, model.PersonalInterests{ID: idVal})
				} else {
					errMsg := fmt.Sprintf("invalid format for %s", interestKey)
					return errors.New(errors.Controller, errors.Message(errMsg), errors.Validation, nil)
				}
				idx++
				if idx > 50 {
					break
				}
			}
			onboarding.PersonalInterests = interests
		} else {
			onboarding = &model.Onboarding{}
		}

		// Get photo from request (following auth pattern)
		photo, err := c.FormFile("photo")
		if err != nil && err != http.ErrMissingFile {
			return errors.New(errors.Controller, "failed to process photo", errors.Validation, err)
		}

		var photoURL string
		if photo != nil {
			// Validate file type
			ext := strings.ToLower(filepath.Ext(photo.Filename))
			allowedExts := map[string]bool{
				".jpg":  true,
				".jpeg": true,
				".png":  true,
			}
			if !allowedExts[ext] {
				return errors.New(errors.Controller, "invalid file type, only JPG, JPEG, and PNG are allowed", errors.Validation, nil)
			}

			// Validate file size (max 5MB)
			if photo.Size > 5*1024*1024 {
				return errors.New(errors.Controller, "file too large, max size is 5MB", errors.Validation, nil)
			}
		}

		googleUserDetails, err := gc.Service.GetUserInfoFromGoogle(accessToken)
		if err != nil {
			return err
		}

		accessData, err := gc.Service.Register(googleUserDetails, onboarding, photo, photoURL, referralCode)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, accessData)
	}
}

func (gc *controller) CallBackFromGoogleLogin() echo.HandlerFunc {
	return func(c echo.Context) error {
		googleRegisterInformation := gc.Service.RegisterInformation()

		if err := c.Bind(&googleRegisterInformation); err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return errors.New(errors.Controller, "invalid google token", errors.Validation, nil)
		}

		googleUserDetails, err := gc.Service.GetUserInfoFromGoogle(googleRegisterInformation.Access)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		user, token, err := gc.Service.Login(googleUserDetails)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		loginResponse := LoginResponseDTO{
			ID:           user.ID,
			Name:         user.Name,
			LastName:     user.LastName,
			Email:        user.Email,
			PhotoURL:     user.PhotoURL,
			ReferralCode: user.ReferralCode,
			Access:       token.Access,
			Refresh:      token.Refresh,
		}

		if googleRegisterInformation.FCMToken != "" {
			err := gc.FirebaseService.Create(c.Request().Context(), user.ID, googleRegisterInformation.FCMToken)
			if err != nil {
				return err
			}
		}

		return c.JSON(http.StatusOK, loginResponse)
	}
}

// Helper
// StringToByte converts a string to a byte (uint8).
// Returns the converted byte and a boolean indicating success.
// An empty string is treated as a failed conversion in this version.
func StringToByte(s string) (byte, bool) {
	if s == "" {
		return 0, false // Treat empty string as a conversion failure
	}
	val, err := strconv.ParseUint(s, 10, 8) // Parse as uint8 (byte)
	if err != nil {
		return 0, false // Conversion failed
	}
	return byte(val), true // Conversion successful
}
