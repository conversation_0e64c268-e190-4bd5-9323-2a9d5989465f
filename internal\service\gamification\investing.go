package gamification

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
)

// Começou a Investir implementation
func (s *service) CheckInvestingAchievement(ctx context.Context, userID string) error {
	// Step 1: Check if user already has Explorer achievement (fail-fast)
	hasAchievement, err := s.Repository.HasAchievement(ctx, userID, investingAchievementIdentifier)
	if err != nil {
		return errors.New(errors.Service, "failed to check if user has investing achievement", errors.Internal, err)
	}
	if hasAchievement {
		// User already has the achievement, no need to check further
		return nil
	}

	// Step 2: Check if the user has the plan Dinbora+ (fail-fast)
	// TODO: Implement this check
	// hasDinboraPlusPlan, err := s.hasDinboraPlusPlan(ctx, userID)
	// if err != nil {
	// 	return errors.New(errors.Service, "failed to check if user has dinbora plus plan", errors.Internal, err)
	// }
	// log.Printf("User %s has dinbora plus plan: %v", userID, hasDinboraPlusPlan)
	// if !hasDinboraPlusPlan {
	// 	// User doesn't have the Dinbora+ plan, doesn't qualify for Investing achievement
	// 	return nil
	// }

	// Step 3: Check if user completed "simulacao-independencia-phase001" challenge (fail-fast)
	hasCompletedChallenge, err := s.hasCompletedSimulacaoIndependenciaChallenge(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check simulacao independencia challenge completion", errors.Internal, err)
	}
	if !hasCompletedChallenge {
		// Challenge not completed, user doesn't qualify for Investing achievement
		return nil
	}

	// Step 4: Check if the user has completed the financial map from the dashboard
	hasCompletedFinancialMap, err := s.hasCompletedFinancialMap(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check financial map completion", errors.Internal, err)
	}
	if !hasCompletedFinancialMap {
		// Financial map not completed, user doesn't qualify for Investing achievement
		return nil
	}

	// Step 5: Check if the user has filled the financial independence simulator
	hasFilledFinancialIndependence, err := s.hasFilledFinancialIndependence(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check financial independence completion", errors.Internal, err)
	}
	if !hasFilledFinancialIndependence {
		// Financial independence not filled, user doesn't qualify for Investing achievement
		return nil
	}

	// Step 6: All checks passed, award the achievement
	return s.awardAchievement(ctx, userID, investingAchievementIdentifier)
}

// Helper methods to award an achievement
func (s *service) hasCompletedSimulacaoIndependenciaChallenge(ctx context.Context, userID string) (bool, error) {
	progression, err := s.ProgressionRepo.FindByUser(ctx, userID)
	if err != nil {
		return false, err
	}

	// Look for "simulacao-independencia-phase001" phase completion in any trail
	for _, trail := range progression.Trails {
		if trail.Challenge != nil {
			for _, phase := range trail.Challenge.Phases {
				if phase.Identifier == investingChallengePhase001Identifier && phase.Completed {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

func (s *service) hasCompletedFinancialMap(ctx context.Context, userID string) (bool, error) {
	financialMap, err := s.DashboardRepo.FindFinancialMap(ctx, userID)
	if err != nil {
		return false, err
	}

	// Check if the financial map has monthlyIncome, strategicFund, totalInvestments and totalAssets
	if financialMap.MonthlyIncome > 0 && financialMap.StrategicFund != nil && financialMap.TotalInvestments > 0 && financialMap.TotalAssets > 0 {
		return true, nil
	}

	return false, nil
}

func (s *service) hasFilledFinancialIndependence(ctx context.Context, userID string) (bool, error) {
	financialIndependence, err := s.DashboardRepo.FindFinancialIndependenceByUser(ctx, userID)
	if err != nil {
		return false, err
	}

	// Check if the financial independence has retirementTargetAmount
	if financialIndependence.RetirementTargetAmount > 0 {
		return true, nil
	}

	return false, nil
}
