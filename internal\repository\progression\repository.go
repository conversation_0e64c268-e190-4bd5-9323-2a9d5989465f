package progression

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
)

type Reader interface {
	Find(ctx context.Context, id string) (*progression.Progression, error)
	FindByUser(ctx context.Context, userId string) (*progression.Progression, error)

	// Optimized method for trail cards
	FindForCards(ctx context.Context, userId string) (map[string]*progression.Trail, error)
}

type Writer interface {
	Create(ctx context.Context, progression *progression.Progression) error
	Update(ctx context.Context, progression *progression.Progression) error
	Delete(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
