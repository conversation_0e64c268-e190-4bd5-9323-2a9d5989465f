package gamification

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
)

type Reader interface {
	// Find user achievement document by ID
	Find(ctx context.Context, id string) (*gamification.UserAchievement, error)

	// Find user achievement document by userID (returns the consolidated document)
	FindByUser(ctx context.Context, userID string) (*gamification.UserAchievement, error)

	// Check if user already has a specific achievement
	HasAchievement(ctx context.Context, userID, achievementIdentifier string) (bool, error)
}

type Writer interface {
	// Create a new achievement for user's achievement document (upsert operation)
	CreateAchievement(ctx context.Context, userID, achievementIdentifier string) error

	// Update an existing user achievement document
	Update(ctx context.Context, userAchievement *gamification.UserAchievement) error

	// Delete a user achievement document
	Delete(ctx context.Context, id string) error

	// Remove a specific achievement from user's achievement document
	RemoveAchievement(ctx context.Context, userID, achievementIdentifier string) error
}

type Repository interface {
	Reader
	Writer
}
