package billing

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type subscriptionMongoDB struct {
	collection *mongo.Collection
}

func NewSubscriptionRepository(db *mongo.Database) SubscriptionRepository {
	repo := &subscriptionMongoDB{
		collection: db.Collection(repository.BILLING_SUBSCRIPTIONS_COLLECTION),
	}

	// Create indexes
	repo.createIndexes()

	return repo
}

func (r *subscriptionMongoDB) createIndexes() {
	ctx := context.Background()

	// Index on userId for finding user subscriptions
	_, err := r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "userId", Value: 1}},
			Options: options.Index().SetName("subscriptionUserId"),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on subscriptions.userId field: %v", err)
	}

	// Compound index on userId and status for finding active user subscriptions
	_, err = r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "userId", Value: 1}, {Key: "status", Value: 1}},
			Options: options.Index().SetName("subscriptionUserIdStatus"),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on subscriptions.userId+status fields: %v", err)
	}

	// Index on provider and providerSubscriptionId for webhook lookups
	_, err = r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "provider", Value: 1}, {Key: "providerSubscriptionId", Value: 1}},
			Options: options.Index().SetName("subscriptionProviderSubscriptionId").SetSparse(true),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on subscriptions.provider+providerSubscriptionId fields: %v", err)
	}

	// Index on endDate for finding expiring subscriptions
	_, err = r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "endDate", Value: 1}},
			Options: options.Index().SetName("subscriptionEndDate"),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on subscriptions.endDate field: %v", err)
	}

	// Index on status for finding subscriptions by status
	_, err = r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "status", Value: 1}},
			Options: options.Index().SetName("subscriptionStatus"),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on subscriptions.status field: %v", err)
	}
}

// Read operations
func (r *subscriptionMongoDB) Find(ctx context.Context, id primitive.ObjectID) (*billing.Subscription, error) {
	var subscription billing.Subscription
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&subscription)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "subscription not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find subscription", errors.Internal, err)
	}

	subscription.ID = subscription.ObjectID.Hex()
	return &subscription, nil
}

func (r *subscriptionMongoDB) FindByUser(ctx context.Context, userID primitive.ObjectID) ([]*billing.Subscription, error) {
	cursor, err := r.collection.Find(ctx, bson.M{"userId": userID})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find user subscriptions", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var subscriptions []*billing.Subscription
	for cursor.Next(ctx) {
		var subscription billing.Subscription
		if err := cursor.Decode(&subscription); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode subscription", errors.Internal, err)
		}
		subscription.ID = subscription.ObjectID.Hex()
		subscriptions = append(subscriptions, &subscription)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding user subscriptions", errors.Internal, err)
	}

	return subscriptions, nil
}

func (r *subscriptionMongoDB) FindActiveByUser(ctx context.Context, userID primitive.ObjectID) ([]*billing.Subscription, error) {
	filter := bson.M{
		"userId": userID,
		"status": bson.M{
			"$in": []billing.SubscriptionStatus{
				billing.SubscriptionStatusActive,
				billing.SubscriptionStatusTrial,
			},
		},
		"endDate": bson.M{"$gt": time.Now()},
	}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find active user subscriptions", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var subscriptions []*billing.Subscription
	for cursor.Next(ctx) {
		var subscription billing.Subscription
		if err := cursor.Decode(&subscription); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode subscription", errors.Internal, err)
		}
		subscription.ID = subscription.ObjectID.Hex()
		subscriptions = append(subscriptions, &subscription)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding active user subscriptions", errors.Internal, err)
	}

	return subscriptions, nil
}

func (r *subscriptionMongoDB) FindByProviderSubscriptionID(ctx context.Context, provider billing.PaymentProvider, providerSubscriptionID string) (*billing.Subscription, error) {
	filter := bson.M{
		"provider":               provider,
		"providerSubscriptionId": providerSubscriptionID,
	}

	var subscription billing.Subscription
	err := r.collection.FindOne(ctx, filter).Decode(&subscription)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "subscription not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find subscription by provider subscription ID", errors.Internal, err)
	}

	subscription.ID = subscription.ObjectID.Hex()
	return &subscription, nil
}

func (r *subscriptionMongoDB) FindExpiring(ctx context.Context, days int) ([]*billing.Subscription, error) {
	expirationDate := time.Now().AddDate(0, 0, days)
	filter := bson.M{
		"status": bson.M{
			"$in": []billing.SubscriptionStatus{
				billing.SubscriptionStatusActive,
				billing.SubscriptionStatusTrial,
			},
		},
		"endDate": bson.M{
			"$gte": time.Now(),
			"$lte": expirationDate,
		},
	}

	cursor, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find expiring subscriptions", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var subscriptions []*billing.Subscription
	for cursor.Next(ctx) {
		var subscription billing.Subscription
		if err := cursor.Decode(&subscription); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode subscription", errors.Internal, err)
		}
		subscription.ID = subscription.ObjectID.Hex()
		subscriptions = append(subscriptions, &subscription)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding expiring subscriptions", errors.Internal, err)
	}

	return subscriptions, nil
}

func (r *subscriptionMongoDB) FindByStatus(ctx context.Context, status billing.SubscriptionStatus) ([]*billing.Subscription, error) {
	cursor, err := r.collection.Find(ctx, bson.M{"status": status})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find subscriptions by status", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var subscriptions []*billing.Subscription
	for cursor.Next(ctx) {
		var subscription billing.Subscription
		if err := cursor.Decode(&subscription); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode subscription", errors.Internal, err)
		}
		subscription.ID = subscription.ObjectID.Hex()
		subscriptions = append(subscriptions, &subscription)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding subscriptions by status", errors.Internal, err)
	}

	return subscriptions, nil
}

// Write operations
func (r *subscriptionMongoDB) Create(ctx context.Context, subscription *billing.Subscription) (string, error) {
	subscription.SetDefaults()

	if err := subscription.Validate(); err != nil {
		return "", err
	}

	result, err := r.collection.InsertOne(ctx, subscription)
	if err != nil {
		return "", errors.New(errors.Repository, "failed to create subscription", errors.Internal, err)
	}

	objectID, ok := result.InsertedID.(primitive.ObjectID)
	if !ok {
		return "", errors.New(errors.Repository, "failed to get inserted subscription ID", errors.Internal, nil)
	}

	return objectID.Hex(), nil
}

func (r *subscriptionMongoDB) Update(ctx context.Context, subscription *billing.Subscription) error {
	if err := subscription.Validate(); err != nil {
		return err
	}

	filter := bson.M{"_id": subscription.ObjectID}
	update := bson.M{"$set": subscription}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update subscription", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "subscription not found", errors.NotFound, nil)
	}

	return nil
}

func (r *subscriptionMongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete subscription", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "subscription not found", errors.NotFound, nil)
	}

	return nil
}
