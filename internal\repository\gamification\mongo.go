package gamification

import (
	"context"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.GAMIFICATION_USER_ACHIEVEMENTS_COLLECTION),
	}

	// Create unique index on userId for efficient user lookups (one document per user)
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "userId", Value: 1}},
			Options: options.Index().SetUnique(true).SetName("user_id_unique"),
		},
	)
	if err != nil {
		log.Println("warning: failed to create unique index on userId field")
	}

	return repo
}

// Reader implementation
func (m *mongoDB) Find(ctx context.Context, id string) (*gamification.UserAchievement, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid achievement ID format", errors.Validation, err)
	}

	filter := bson.D{{Key: "_id", Value: objectID}}

	var userAchievement gamification.UserAchievement
	err = m.collection.FindOne(ctx, filter).Decode(&userAchievement)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "user achievement not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find user achievement", errors.Internal, err)
	}

	userAchievement.SetID()
	return &userAchievement, nil
}

func (m *mongoDB) FindByUser(ctx context.Context, userID string) (*gamification.UserAchievement, error) {
	filter := bson.D{{Key: "userId", Value: userID}}

	var userAchievement gamification.UserAchievement
	err := m.collection.FindOne(ctx, filter).Decode(&userAchievement)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "user achievements not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find user achievements", errors.Internal, err)
	}

	userAchievement.SetID()
	return &userAchievement, nil
}

func (m *mongoDB) HasAchievement(ctx context.Context, userID, achievementIdentifier string) (bool, error) {
	filter := bson.D{
		{Key: "userId", Value: userID},
		{Key: "achievements.identifier", Value: achievementIdentifier},
	}

	count, err := m.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, errors.New(errors.Repository, "failed to check if user has achievement", errors.Internal, err)
	}

	return count > 0, nil
}

// Writer implementation
func (m *mongoDB) CreateAchievement(ctx context.Context, userID, achievementIdentifier string) error {
	now := time.Now()

	// First, try to find existing document
	filter := bson.D{{Key: "userId", Value: userID}}

	var existingDoc gamification.UserAchievement
	err := m.collection.FindOne(ctx, filter).Decode(&existingDoc)

	if err == mongo.ErrNoDocuments {
		// Document doesn't exist, create new one
		newDoc := &gamification.UserAchievement{
			UserID: userID,
			Achievements: []*gamification.Achievement{
				{
					Identifier: achievementIdentifier,
					EarnedAt:   now,
				},
			},
			CreatedAt: now,
			UpdatedAt: now,
		}

		_, err = m.collection.InsertOne(ctx, newDoc)
		if err != nil {
			if mongo.IsDuplicateKeyError(err) {
				// Race condition: document was created by another request
				// Try to add achievement to existing document
				return m.addAchievementToExisting(ctx, userID, achievementIdentifier, now)
			}
			return errors.New(errors.Repository, "failed to create achievement document", errors.Internal, err)
		}
		return nil
	} else if err != nil {
		return errors.New(errors.Repository, "failed to find user achievement document", errors.Internal, err)
	}

	// Document exists, add achievement to it
	return m.addAchievementToExisting(ctx, userID, achievementIdentifier, now)
}

// Helper method to add achievement to existing document
func (m *mongoDB) addAchievementToExisting(ctx context.Context, userID, achievementIdentifier string, earnedAt time.Time) error {
	filter := bson.D{{Key: "userId", Value: userID}}

	update := bson.D{
		{Key: "$addToSet", Value: bson.D{
			{Key: "achievements", Value: bson.D{
				{Key: "identifier", Value: achievementIdentifier},
				{Key: "earnedAt", Value: earnedAt},
			}},
		}},
		{Key: "$set", Value: bson.D{
			{Key: "updatedAt", Value: earnedAt},
		}},
	}

	result, err := m.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to create achievement", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "user achievement document not found", errors.NotFound, nil)
	}

	// Check if achievement was actually added (ModifiedCount will be 0 if it was a duplicate)
	if result.ModifiedCount == 0 {
		return errors.New(errors.Repository, "user already has this achievement", errors.Conflict, nil)
	}

	return nil
}

func (m *mongoDB) Update(ctx context.Context, userAchievement *gamification.UserAchievement) error {
	userAchievement.PrepareUpdate()

	filter := bson.D{{Key: "_id", Value: userAchievement.ObjectID}}
	update := bson.D{{Key: "$set", Value: userAchievement}}

	result, err := m.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update user achievement", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "user achievement not found for update", errors.NotFound, nil)
	}

	return nil
}

func (m *mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid achievement ID format", errors.Validation, err)
	}

	filter := bson.D{{Key: "_id", Value: objectID}}

	result, err := m.collection.DeleteOne(ctx, filter)
	if err != nil {
		return errors.New(errors.Repository, "failed to delete user achievement", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "user achievement not found for deletion", errors.NotFound, nil)
	}

	return nil
}

func (m *mongoDB) RemoveAchievement(ctx context.Context, userID, achievementIdentifier string) error {
	filter := bson.D{{Key: "userId", Value: userID}}

	update := bson.D{
		{Key: "$pull", Value: bson.D{
			{Key: "achievements", Value: bson.D{
				{Key: "identifier", Value: achievementIdentifier},
			}},
		}},
		{Key: "$set", Value: bson.D{
			{Key: "updatedAt", Value: time.Now()},
		}},
	}

	result, err := m.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to remove achievement", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "user achievement document not found", errors.NotFound, nil)
	}

	return nil
}
