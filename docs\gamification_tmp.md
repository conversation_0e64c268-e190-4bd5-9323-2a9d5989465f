Implement gamification package that will controll the achievements of the user. It need to look at many packages in the system (content, progression, financialdna, dreamboard, financialsheet, dashboard, vault). This system will control to give or not an achievement for the user. If the user follow certain rules I will give him one of the achievements from the content package.

Let start with the first achievement "DNA"

The achievement with the identifier "DNA" will be given to the user when, have the phase challenge "perfil-financeiro" completed and in the financialdna has completed all the members information (father, mother, grandfather, grandmother, etc). A member is considered completed if has a financial status.

Triggers
A user saves their final piece of Financial DNA information (update one of the family members). The financialdna service updates the user's document in the financial_dna collection. After the successful save, it calls the gamification service. The gamification service has access to financialdna repository. The financialdna service has access to gamification service.
Action: financialdna service tells gamification service: "Check on this user, something important just happened."

A user register a progression in the progression service. The progression service updates the user's document in the progression collection. After the successful save, it calls the gamification service. The gamification service has access to progression repository. The progression service has access to gamification service.
Action: progression service tells gamification service: "Check on this user, something important just happened."

A user query (find) for his achievements in the gamification service. Old users could have the achievement already, before the implementation of the gamification service.
Action: gamification service checks if the user comply with the rules and update the user gamification document and return.

The Check (in the gamification service)
The gamification service now runs a series of checks by querying different MongoDB collections. It stops if any check fails.
Check A: Already Awarded?
Question: Does this user already have the "DNA" achievement?
Check B: Challenge Complete?
Question: Has the user completed the "Perfil Financeiro" challenge?
Check C: DNA Complete?
Question: Is the user's financial DNA fully filled out?

The Decision
The gamification service has finished all checks. If it never stopped, it means the user is eligible.
Action: All rules passed. It's time to give the user the achievement.

The Award
The gamification service creates a new document in the achievements collection.

This flow keeps the gamification package as the central "brain," while other services simply report events. 

This is the demonstration of only ONE achievement, but the same pattern can be applied to all of them using different packages (dreamboard, financialsheet, dashboard) AND different rules depending on the achievement. For now let's focus on the DNA achievement.

Simplify the implementation trigger -> action -> check -> decision -> award and follow the system patterns.